# Facebook Firebase Connection Checklist

## ✅ Step-by-Step Connection Guide

### Facebook App Setup
- [ ] Go to [Facebook for Developers](https://developers.facebook.com/)
- [ ] Select your existing Facebook app
- [ ] Copy **App ID** from Settings → Basic
- [ ] Copy **App Secret** from Settings → Basic  
- [ ] Copy **Client Token** from Settings → Advanced

### Update Android Configuration
- [ ] Open `android/app/src/main/res/values/strings.xml`
- [ ] Replace `PASTE_YOUR_FACEBOOK_APP_ID_HERE` with your actual App ID
- [ ] Replace `PASTE_YOUR_FACEBOOK_CLIENT_TOKEN_HERE` with your actual Client Token
- [ ] Update `fbPASTE_YOUR_FACEBOOK_APP_ID_HERE` with `fb` + your App ID

**Example:**
```xml
<string name="facebook_app_id">123456789012345</string>
<string name="facebook_client_token">********************************</string>
<string name="fb_login_protocol_scheme">fb123456789012345</string>
```

### Firebase Console Setup
- [ ] Go to [Firebase Console](https://console.firebase.google.com/)
- [ ] Select project: `coffee-vortex`
- [ ] Go to Authentication → Sign-in method
- [ ] Click on Facebook provider
- [ ] Click "Enable"
- [ ] Enter your Facebook App ID
- [ ] Enter your Facebook App Secret
- [ ] **Copy the OAuth redirect URI** (important!)
- [ ] Click Save

### Facebook App Configuration
- [ ] Go back to Facebook app dashboard
- [ ] Go to Facebook Login → Settings
- [ ] Paste Firebase OAuth redirect URI into "Valid OAuth Redirect URIs"
- [ ] Save changes

### Android Platform Setup
- [ ] In Facebook app, go to Settings → Basic
- [ ] Click "+ Add Platform" → Android
- [ ] Package Name: `com.example.coffe_vortex`
- [ ] Class Name: `com.example.coffe_vortex.MainActivity`

### Generate Key Hash
Run this command in terminal:
```bash
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android | openssl sha1 -binary | openssl base64
```

- [ ] Copy the generated hash
- [ ] Add to Facebook app Settings → Basic → Key Hashes

### Test Connection
- [ ] Run `flutter clean && flutter pub get`
- [ ] Run `flutter run`
- [ ] Go to login screen
- [ ] Click "Continue with Facebook"
- [ ] Complete Facebook login
- [ ] Verify app receives user data

## 🚨 Common Issues

### "Invalid key hash"
- Make sure you're using the debug keystore for development
- Regenerate hash if needed
- Check hash is added correctly to Facebook app

### "OAuth redirect URI mismatch"  
- Copy exact URI from Firebase (with https://)
- No extra spaces or characters
- Make sure it's added to Facebook app settings

### "App not configured for Facebook Login"
- Verify Facebook Login product is added to your app
- Check package name matches exactly
- Make sure app is published/live in Facebook

### Login button not working
- Check internet permission in AndroidManifest.xml
- Verify all configuration steps completed
- Test on real device (not emulator)

## 📱 Testing Tips

1. **Use Real Device**: Facebook login works better on real devices
2. **Check Logs**: Use `adb logcat | grep -i facebook` for debugging
3. **Facebook App**: Having Facebook app installed can improve experience
4. **Network**: Ensure stable internet connection

## ✅ Success Indicators

When everything is working:
- Facebook button appears on login screen
- Clicking opens Facebook authentication
- User can complete login in browser/Facebook app
- App receives user profile data
- User is logged in and navigated to main screen
- Profile screen shows Facebook user information

## 🔧 If Still Not Working

1. Double-check all configuration values
2. Ensure Facebook app is in "Live" mode
3. Verify Firebase project settings
4. Check Android package name matches everywhere
5. Regenerate and re-add key hash
6. Test with different Facebook account

## 📞 Need Help?

If you're still having issues, please share:
- Your Facebook App ID (safe to share)
- Any error messages you see
- Screenshots of Firebase/Facebook configuration
- Android logs when testing login
