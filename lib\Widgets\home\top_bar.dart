import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:test_pro/screen/notification_screen.dart';
import 'package:test_pro/screen/search_screen.dart';
import 'package:test_pro/services/auth_service.dart';
import 'package:test_pro/theme/app_theme.dart';
import 'package:test_pro/controllers/user_controller.dart';

class TopBar extends StatefulWidget {
  const TopBar({super.key});

  @override
  State<TopBar> createState() => _TopBarState();
}

class _TopBarState extends State<TopBar> {
  final TextEditingController _searchController = TextEditingController();
  final UserController userController = Get.find<UserController>();

  String _timeText = '';
  String? selectedCategory;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _clock();
  }

  Future<void> _clock() async {
    final now = DateTime.now();
    final hour = now.hour;

    String period;
    if (hour < 12) {
      period = "goodmorning".tr;
    } else if (hour < 18) {
      period = "goodafternoon".tr;
    } else {
      period = "goodevening".tr;
    }

    setState(() {
      _timeText = ' $period, have a nice day.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 160,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(10),
          bottomRight: Radius.circular(10),
        ),
        color: AppTheme.blogBackground,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16).copyWith(top: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 50),
                Obx(() => Row(
                  children: [
                    Text(
                      "hi".tr,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 25,
                        color: const Color.fromARGB(221, 255, 255, 255),
                      ),
                    ),
                    Text(
                      ", Mr. ${userController.userName} 👋",
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 25,
                        color: const Color.fromARGB(221, 255, 255, 255),
                      ),
                    ),
                  ],
                )),
                const SizedBox(height: 8),
                Expanded(
                  child: Text(
                    _timeText.isEmpty ? '' : _timeText,
                    style: const TextStyle(fontSize: 15, color: Colors.white),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            Column(
              children: [
                const SizedBox(height: 50),
                Row(
                  children: [
                    // ElevatedButton(onPressed: (){
                    //   Navigator.push(context, MaterialPageRoute(builder: (context)=> const OutletFinderScreen()));
                    // }, child: Text('Outlate')),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SearchScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.all(12),
                        minimumSize: const Size(48, 48),
                        backgroundColor: Colors.white,
                        elevation: 2,
                      ),
                      child: const Icon(Icons.search, size: 24, color: Colors.black54),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () async {
                        final navigator = Navigator.of(context);

                        // Check authentication first
                        final hasAuth = await AuthService.requireAuth(
                          context,
                          message: 'Please log in to view your notifications.',
                        );

                        if (!hasAuth) return;

                        navigator.push(
                          MaterialPageRoute(
                            builder: (context) => const NotificationScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.all(12),
                        minimumSize: const Size(48, 48),
                        backgroundColor: Colors.white,
                        elevation: 2,
                      ),
                      child: const Icon(Icons.notifications_active, size: 24, color: Colors.black54),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
