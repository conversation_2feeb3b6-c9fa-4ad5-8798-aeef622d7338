import 'package:flutter/material.dart';
import 'package:test_pro/route/route_screen.dart';
import 'package:test_pro/theme/app_theme.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<String> _titles = [
    "Welcome to Our App",
    "How to Use the App",
    "Get Started Now",
  ];

  final List<String> _descriptions = [
    "This app helps you manage tasks efficiently.",
    "Tap the buttons to navigate through the app.",
    "Click 'Start' to begin your journey with us!",
  ];

  final List<String> _images = [
    'assets/images/coffee_shop.png',
    'assets/images/coffee_welcome.png',
    'assets/images/coffee_machine_welcome.png',
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onNextPressed() {
    if (_currentPage < _titles.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    } else {
      AppRoute.key.currentState?.pushNamed(AppRoute.loginScreen);
    }
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_titles.length, (index) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4.0),
          height: 6.0,
          width: index == _currentPage ? 30.0 : 20.0,
          decoration: BoxDecoration(
            color: index == _currentPage
                ? Color.fromARGB(255, 77, 96, 36)
                : const Color.fromARGB(77, 251, 251, 251),
            borderRadius: BorderRadius.circular(3.0),
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue, Colors.purple],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            /// PageView with images & text
            PageView.builder(
              controller: _pageController,
              itemCount: _titles.length,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemBuilder: (context, index) {
                double borderRadius = index == _currentPage ? 0.0 : 40.0;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  child: Stack(
                    children: [
                      // Background image with rounded corners
                      ClipRRect(
                        borderRadius: BorderRadius.circular(borderRadius),
                        child: Image.asset(
                          _images[index],
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                        ),
                      ),
                      // Text overlay
                      Positioned(
                        top: 350,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.black45,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      _titles[index],
                                      style: const TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    Text(
                                      _descriptions[index],
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                ),
                              ),
                              SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            /// Next button at bottom center
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 70.0),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 160,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(13),
                    ),
                    backgroundColor: AppTheme.button,
                  ),
                  onPressed: _onNextPressed,
                  child: Text(
                    _currentPage < _titles.length - 1 ? 'Next' : 'Start',
                    style: const TextStyle(fontSize: 18, color: Colors.white),
                  ),
                ),
              ),
            ),

            Align(
              alignment: Alignment.bottomCenter,
              child: TextButton(
                onPressed: () {
                  AppRoute.key.currentState?.pushNamed(AppRoute.loginScreen);
                },
                child: const Text(
                  'Skip This Step',
                  style: TextStyle(
                    fontSize: 17,
                    letterSpacing: 1.1,
                    color: Color.fromARGB(120, 255, 255, 255),
                  ),
                ),
              ),
            ),

            /// Skip button at top right
            Positioned(
              bottom: 180,
              right: 100,
              left: 100,
              child: TextButton(
                onPressed: () {
                  AppRoute.key.currentState?.pushNamed(AppRoute.loginScreen);
                },
                child: _buildPageIndicator(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
