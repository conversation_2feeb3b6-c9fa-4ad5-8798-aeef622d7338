import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:test_pro/data/db_manager.dart';
import 'package:test_pro/route/route_screen.dart';
import 'package:test_pro/screen/splash_screen.dart';
import 'package:get/get.dart';
import 'package:test_pro/screen/translate/app_translate.dart';
import 'package:test_pro/services/theme_service.dart';
import 'package:test_pro/services/translation_service.dart';
import 'package:test_pro/controllers/language_controller.dart';
import 'package:test_pro/controllers/user_controller.dart';
import 'package:test_pro/theme/app_theme.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase Congfigure
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  DBManager.instance.database;

  await ThemeService().initTheme();

  await TranslationService.init();

  // Initialize controllers
  Get.put(LanguageController());
  Get.put(UserController());


  runApp(const MyApp());
}

Future<void> insertProduct() async {
  await DBManager.instance.database;

 }


class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: ThemeService(),
      builder: (context, child) {
        return Obx(() => GetMaterialApp(
          translations: AppTranslation(),
          locale: Get.find<LanguageController>().currentLocale,
          fallbackLocale: const Locale('en', 'US'),
          debugShowCheckedModeBanner: false,
          title: 'Coffee Vortex',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeService().themeMode,
          home: const SplashScreen(),
          initialRoute: AppRoute.splashScreen,
          onGenerateRoute: AppRoute.onGenerateRoute,
          navigatorKey: AppRoute.key,
        ));
      },
    );
  }
}
