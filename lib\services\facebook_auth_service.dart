import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test_pro/controllers/user_controller.dart';

class FacebookAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FacebookAuth _facebookAuth = FacebookAuth.instance;

  /// Sign in with Facebook
  static Future<UserCredential?> signInWithFacebook() async {
    try {
      // Trigger the sign-in flow
      final LoginResult loginResult = await _facebookAuth.login(
        permissions: ['email', 'public_profile'],
      );

      // Check if login was successful
      if (loginResult.status == LoginStatus.success) {
        // Get the access token
        final AccessToken accessToken = loginResult.accessToken!;

        // Create a credential from the access token
        final OAuthCredential facebookAuthCredential =
            FacebookAuthProvider.credential(accessToken.tokenString);

        // Sign in to Firebase with the Facebook credential
        final UserCredential userCredential = await _auth
            .signInWithCredential(facebookAuthCredential);

        if (userCredential.user != null) {
          // Save user data to SharedPreferences
          await _saveUserData(userCredential.user!);

          // Update UserController
          await _updateUserController();

          return userCredential;
        }
      } else if (loginResult.status == LoginStatus.cancelled) {
        // User cancelled the login
        Get.snackbar(
          'Login Cancelled',
          'Facebook login was cancelled',
          snackPosition: SnackPosition.TOP,
        );
      } else {
        // Login failed
        Get.snackbar(
          'Login Failed',
          'Facebook login failed: ${loginResult.message}',
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      // Handle errors
      Get.snackbar(
        'Error',
        'Failed to sign in with Facebook: $e',
        snackPosition: SnackPosition.TOP,
      );
    }
    return null;
  }

  /// Save user data to SharedPreferences
  static Future<void> _saveUserData(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Extract username from display name or email
      final username = user.displayName ?? 
                      user.email?.split('@')[0] ?? 
                      'Facebook User';

      await prefs.setString('username', username);
      await prefs.setString('email', user.email ?? '');
      await prefs.setString('login_method', 'facebook');
      
      // Save additional Facebook data if available
      if (user.photoURL != null) {
        await prefs.setString('profile_image_url', user.photoURL!);
      }
    } catch (e) {
      print('Error saving user data: $e');
    }
  }

  /// Update UserController after successful login
  static Future<void> _updateUserController() async {
    try {
      // Small delay to ensure SharedPreferences are saved
      await Future.delayed(const Duration(milliseconds: 100));

      // Refresh UserController to update user state
      final userController = Get.find<UserController>();
      await userController.forceRefreshAfterLogin();
    } catch (e) {
      print('Error updating user controller: $e');
    }
  }

  /// Sign out from Facebook and Firebase
  static Future<void> signOut() async {
    try {
      // Sign out from Firebase
      await _auth.signOut();
      
      // Sign out from Facebook
      await _facebookAuth.logOut();

      // Clear SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('username');
      await prefs.remove('email');
      await prefs.remove('login_method');
      await prefs.remove('profile_image_url');

      // Update UserController to guest state
      final userController = Get.find<UserController>();
      userController.clearUser();

      Get.snackbar(
        'Signed Out',
        'You have been signed out successfully',
        snackPosition: SnackPosition.TOP,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to sign out: $e',
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  /// Check if user is currently signed in with Facebook
  static Future<bool> isSignedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final loginMethod = prefs.getString('login_method');
      return loginMethod == 'facebook' && _auth.currentUser != null;
    } catch (e) {
      return false;
    }
  }

  /// Get current Facebook user data
  static Future<Map<String, dynamic>?> getCurrentUserData() async {
    try {
      if (await isSignedIn()) {
        final userData = await _facebookAuth.getUserData();
        return userData;
      }
    } catch (e) {
      print('Error getting Facebook user data: $e');
    }
    return null;
  }
}
