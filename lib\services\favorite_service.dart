import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:test_pro/models/favorite_model.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/auth_service.dart';

class FavoriteService {
  static Future<String> _getFilePath() async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}/favorites.json';
  }

  static Future<List<FavoriteItem>> getFavorites() async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return []; // Return empty list for guest users
    }

    final path = await _getFilePath();
    final file = File(path);

    if (!(await file.exists())) return [];

    final lines = await file.readAsLines();
    return lines.map((line) => FavoriteItem.fromMap(jsonDecode(line))).toList();
  }

  static Future<void> _saveFavorites(List<FavoriteItem> favorites) async {
    final path = await _getFilePath();
    final file = File(path);
    final sink = file.openWrite();
    for (final item in favorites) {
      sink.writeln(jsonEncode(item.toMap()));
    }
    await sink.close();
  }

  static Future<bool> isFavorite(int productId) async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return false; // Guest users have no favorites
    }

    final favorites = await getFavorites();
    return favorites.any((item) => item.productId == productId);
  }

  static Future<void> addToFavorites(Product product) async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return; // Don't save favorites for guest users
    }

    final favorites = await getFavorites();

    // Check if already in favorites
    if (favorites.any((item) => item.productId == (product.id ?? 0))) {
      return;
    }

    final favoriteItem = FavoriteItem(
      productId: product.id ?? 0,
      name: product.name,
      category: product.category,
      price: product.price,
      image: product.image,
      description: product.description,
    );

    favorites.add(favoriteItem);
    await _saveFavorites(favorites);
  }

  static Future<void> removeFromFavorites(int productId) async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return; // Don't modify favorites for guest users
    }

    final favorites = await getFavorites();
    favorites.removeWhere((item) => item.productId == productId);
    await _saveFavorites(favorites);
  }

  static Future<void> toggleFavorite(Product product) async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return; // Don't toggle favorites for guest users
    }

    final productId = product.id ?? 0;
    final isCurrentlyFavorite = await isFavorite(productId);

    if (isCurrentlyFavorite) {
      await removeFromFavorites(productId);
    } else {
      await addToFavorites(product);
    }
  }
}
