# Firebase Facebook Authentication Setup Guide

## Overview
This guide will help you set up Facebook authentication with Firebase for your Flutter app.

## Prerequisites
- Firebase project already configured (✅ Done)
- Flutter app with Firebase Auth (✅ Done)
- Facebook Developer Account

## Step 1: Create Facebook App

### 1.1 Go to Facebook Developers
1. Visit [Facebook for Developers](https://developers.facebook.com/)
2. Click "My Apps" → "Create App"
3. Choose "Consumer" as app type
4. Fill in app details:
   - **App Name**: Coffee Vortex
   - **App Contact Email**: Your email
   - **App Purpose**: Authentication for your coffee app

### 1.2 Configure Facebook Login
1. In your Facebook app dashboard, go to "Add a Product"
2. Find "Facebook Login" and click "Set Up"
3. Choose "Android" platform
4. Add your package name: `com.example.coffe_vortex`
5. Add your main activity: `com.example.coffe_vortex.MainActivity`

### 1.3 Get Facebook App Credentials
1. Go to Settings → Basic in your Facebook app
2. Copy your **App ID** and **App Secret**
3. Note down the **Client Token** from Advanced settings

## Step 2: Configure Firebase Console

### 2.1 Enable Facebook Authentication
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `coffee-vortex`
3. Go to Authentication → Sign-in method
4. Click on "Facebook" provider
5. Click "Enable"
6. Enter your Facebook App ID and App Secret
7. Copy the OAuth redirect URI provided by Firebase
8. Click "Save"

### 2.2 Add OAuth Redirect URI to Facebook
1. Go back to your Facebook app dashboard
2. Go to Facebook Login → Settings
3. Add the Firebase OAuth redirect URI to "Valid OAuth Redirect URIs"
4. Save changes

## Step 3: Update Android Configuration

### 3.1 Update strings.xml
Replace the placeholders in `android/app/src/main/res/values/strings.xml`:

```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Coffee Vortex</string>
    
    <!-- Replace with your actual Facebook App ID -->
    <string name="facebook_app_id">YOUR_ACTUAL_FACEBOOK_APP_ID</string>
    <!-- Replace with your actual Facebook Client Token -->
    <string name="facebook_client_token">YOUR_ACTUAL_FACEBOOK_CLIENT_TOKEN</string>
    <string name="fb_login_protocol_scheme">fbYOUR_ACTUAL_FACEBOOK_APP_ID</string>
</resources>
```

### 3.2 Generate Key Hash (Required for Facebook)
Run this command in your terminal:

**For Debug:**
```bash
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore | openssl sha1 -binary | openssl base64
```

**For Release:**
```bash
keytool -exportcert -alias YOUR_RELEASE_KEY_ALIAS -keystore YOUR_RELEASE_KEY_PATH | openssl sha1 -binary | openssl base64
```

Add the generated key hash to your Facebook app:
1. Go to Settings → Basic in Facebook app
2. Add the key hash to "Key Hashes" field

## Step 4: Install Dependencies

Run this command to install the new dependency:
```bash
flutter pub get
```

## Step 5: Test Facebook Login

### 5.1 Build and Run
```bash
flutter run
```

### 5.2 Test the Login Flow
1. Open the app
2. Go to login screen
3. Click "Continue with Facebook"
4. Complete Facebook login
5. Verify user is logged in with Facebook data

## Step 6: iOS Configuration (Optional)

If you plan to support iOS, add this to `ios/Runner/Info.plist`:

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>fbauth</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>fbYOUR_FACEBOOK_APP_ID</string>
        </array>
    </dict>
</array>
<key>FacebookAppID</key>
<string>YOUR_FACEBOOK_APP_ID</string>
<key>FacebookClientToken</key>
<string>YOUR_FACEBOOK_CLIENT_TOKEN</string>
<key>FacebookDisplayName</key>
<string>Coffee Vortex</string>
```

## Troubleshooting

### Common Issues:

1. **"Invalid key hash"**
   - Regenerate key hash and add to Facebook app
   - Make sure you're using the correct keystore

2. **"App not configured for Facebook Login"**
   - Check Facebook app settings
   - Verify package name matches exactly

3. **"OAuth redirect URI mismatch"**
   - Copy exact URI from Firebase to Facebook app
   - Check for trailing slashes or extra characters

4. **Login button not working**
   - Check internet permission in AndroidManifest.xml
   - Verify Facebook app is in development/live mode

### Debug Steps:
1. Check Firebase console for authentication events
2. Check Android logs for Facebook SDK errors
3. Verify all configuration values are correct
4. Test with Facebook's debug tools

## Security Notes

- Never commit Facebook App Secret to version control
- Use environment variables for sensitive data in production
- Regularly rotate Facebook app credentials
- Monitor authentication logs for suspicious activity

## Next Steps

After successful setup:
1. Test login flow thoroughly
2. Handle edge cases (network errors, cancelled login)
3. Implement logout functionality
4. Add user profile management
5. Consider adding other social login providers
