import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:test_pro/controllers/user_controller.dart';
import 'package:test_pro/services/auth_service.dart';

class AuthTestWidget extends StatelessWidget {
  const AuthTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final UserController userController = Get.find<UserController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Authentication Test'),
        backgroundColor: Colors.green,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current User State
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current User State:',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    GetBuilder<UserController>(
                      builder: (controller) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('UserController.userName: ${controller.userName}'),
                          Text('UserController.userEmail: ${controller.userEmail}'),
                          Text('UserController.isLoggedIn: ${controller.isLoggedIn}'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // AuthService State
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AuthService State:',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    FutureBuilder<Map<String, dynamic>>(
                      future: _getAuthServiceState(),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          final data = snapshot.data!;
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('AuthService.isLoggedIn(): ${data['isLoggedIn']}'),
                              Text('AuthService.getCurrentUsername(): ${data['username']}'),
                              Text('AuthService.getCurrentUserEmail(): ${data['email']}'),
                              Text('AuthService.isGuest(): ${data['isGuest']}'),
                            ],
                          );
                        }
                        return const CircularProgressIndicator();
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Authentication Logic Test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Authentication Logic:',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    GetBuilder<UserController>(
                      builder: (controller) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            controller.userName.toLowerCase() == 'guest' 
                              ? '❌ Should require login (Guest user)'
                              : '✅ Should NOT require login (Logged in user)',
                            style: TextStyle(
                              color: controller.userName.toLowerCase() == 'guest' 
                                ? Colors.red 
                                : Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Expected behavior:',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            controller.userName.toLowerCase() == 'guest'
                              ? '• Show login alerts for cart/favorites\n• Allow browsing products'
                              : '• No login alerts\n• Full access to all features',
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Actions:',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () async {
                            await userController.forceRefreshAfterLogin();
                            Get.snackbar('Refreshed', 'User state refreshed');
                          },
                          child: const Text('Refresh State'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () async {
                            await userController.debugUserState();
                            Get.snackbar('Debug', 'Check console for debug info');
                          },
                          child: const Text('Debug State'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () async {
                        final hasAuth = await AuthService.requireAuth(
                          context,
                          message: 'Test authentication check',
                        );
                        Get.snackbar(
                          'Auth Test', 
                          hasAuth ? 'Authentication passed!' : 'Authentication failed!',
                          backgroundColor: hasAuth ? Colors.green : Colors.red,
                          colorText: Colors.white,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                      ),
                      child: const Text(
                        'Test Auth Check',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<Map<String, dynamic>> _getAuthServiceState() async {
    final authService = AuthService();
    return {
      'isLoggedIn': await authService.isLoggedIn(),
      'username': await authService.getCurrentUsername(),
      'email': await authService.getCurrentUserEmail(),
      'isGuest': await authService.isGuest(),
    };
  }
}
