import 'package:flutter/material.dart';
import 'package:test_pro/models/outlet_model.dart';

class PickupDelivery extends StatelessWidget {
  final Outlet outlet;

  const PickupDelivery({
    super.key,
    required this.outlet,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
                    children: [
                      if (outlet.hasPickup)
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.green.withValues(alpha: 0.1),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.shopping_bag_outlined, size: 20, color: Colors.green),
                                SizedBox(width: 8),
                                Text('Pick Up', style: TextStyle(color: Colors.green)),
                              ],
                            ),
                          ),
                        ),
                      if (outlet.hasPickup && outlet.hasDelivery)
                        const SizedBox(width: 12),
                      if (outlet.hasDelivery)
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.blue.withValues(alpha: 0.1),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.delivery_dining, size: 20, color: Colors.blue),
                                SizedBox(width: 8),
                                Text('Delivery', style: TextStyle(color: Colors.blue)),
                              ],
                            ),
                          ),
                        ),
                    ],
                  );
  }
}