import 'package:flutter/material.dart';
import 'package:test_pro/route/route_screen.dart';
import 'package:test_pro/theme/app_theme.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        AppRoute.key.currentState?.pushNamed(AppRoute.welcomeScreen);
      },
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(gradient: AppTheme.welcome),
          child: Safe<PERSON><PERSON>(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.coffee_maker,
                      size: 100,
                      color: Colors.redAccent,
                    ),
                    const SizedBox(height: 14),
                    Text(
                      "Vortex Coffee",
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color.fromARGB(221, 255, 255, 255),
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
