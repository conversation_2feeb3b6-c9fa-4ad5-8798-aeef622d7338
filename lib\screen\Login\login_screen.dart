import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test_pro/route/route_screen.dart';
import 'package:test_pro/controllers/user_controller.dart';
import 'package:test_pro/services/facebook_auth_service.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isValidMail = false;
  bool _isValidPassword = false;
  bool _isChecked = false;
   bool _rememberMe = false;
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  var imageIcon = {
    "facebook": "assets/images/facebook.png",
    "google": "assets/images/google.png",
    "iphone": "assets/images/iphone.png",
  };

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            _topBar,
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 32),
                      _emailField,
                      const SizedBox(height: 16),
                      _passwordField,
                      const SizedBox(height: 2),
                      _buildCheck,
                      _loginButton,
                      const SizedBox(height: 15),
                      _textLoginWith,
                      const SizedBox(height: 20),
                      _socialLogin,
                      _navigateToRegister,
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _buildCheck {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          
          Checkbox(
            value: _isValidMail && _isValidPassword && _rememberMe,
            isError: false,
            onChanged: (value) {
              setState(() {
                _isChecked = value!;
                if (_isChecked) {
                   _rememberMe = value;
                  _emailController.text = "<EMAIL>";
                  _passwordController.text = "123456";
                } else {
                  _emailController.clear();
                  _passwordController.clear();
                }
              });
            },
          ),
          TextButton(
            onPressed: () {
              AppRoute.key.currentState?.pushNamed(AppRoute.forgotPassword);
            },
            child: Text('forgotPassword'.tr),
          ),
        ],
      ),
    );
  }
  
  Widget get _topBar {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(10),
          bottomRight: Radius.circular(10),
        ),
        color: const Color.fromARGB(255, 255, 86, 86),
      ),
      child: Stack(
        children: [
          Positioned(
            right: -75,
            bottom: -85,
            child: Image.asset(
              "assets/images/coffee/cfe.png",
              width: 220,
              fit: BoxFit.contain,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16).copyWith(top: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //IconButton(onPressed: (){}, icon: Icon(Icons.arrow_back)),
                SizedBox(height: 70),
                Text(
                  '${'hi'.tr}, ${'welcome'.tr}!',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                Expanded(
                  child: Text(
                    "Lorem ipsum dolor sit amet, consectetur\nadipiscing elit, sed do eiusmod.",
                    style: TextStyle(fontSize: 14, color: Colors.white70),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget get _textLoginWith {
    return GestureDetector(
      onTap: () async {
        // Clear any stored credentials and set user as guest
        await _continueAsGuest();
        AppRoute.key.currentState?.pushNamed(AppRoute.mainScreen);
      },
      child: Text('continue'.tr),
    );
  }

  /// Continue as guest - clear stored credentials and set guest state
  Future<void> _continueAsGuest() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear any stored login credentials
      await prefs.remove('username');
      await prefs.remove('email');
      await prefs.remove('password');

      // Clear user data and set as guest in the controller
      final userController = Get.find<UserController>();
      userController.clearUser();

    } catch (e) {
      // Handle error silently - guest mode should always work
    }
  }

  Widget get _emailField {
    return TextFormField(
      onChanged: (value) {
        if (value.contains("@gmail")) {
          setState(() {
            _isValidMail = true;
          });
        } else {
          setState(() {
            _isValidMail = false;
          });
        }
      },
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: "Email",
        prefixIcon: const Icon(Icons.email),
        suffix: _isValidMail
            ? Icon(Icons.check_circle, color: Colors.green)
            : Icon(Icons.check_circle, color: Colors.grey),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter your email";
        }
        if (!RegExp(r'\S+@\S+\.\S+').hasMatch(value)) {
          return "Please enter a valid email";
        }
        return null;
      },
    );
  }

  Widget get _passwordField {
    return TextFormField(
      onChanged: (value) {
        if (value.length >= 6) {
          setState(() {
            _isValidPassword = true;
          });
        } else {
          setState(() {
            _isValidPassword = false;
          });
        }
      },
      controller: _passwordController,
      obscureText: true,
      decoration: InputDecoration(
        labelText: "Password",
        prefixIcon: const Icon(Icons.lock),
        suffix: _isValidPassword
            ? Icon(Icons.check_circle, color: Colors.green)
            : Icon(Icons.check_circle, color: Colors.grey),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter your password";
        }
        if (value.length < 6) {
          return "Password must be at least 6 characters";
        }
        return null;
      },
    );
  }

  Widget get _socialLogin {
    return Column(
      children: [
        // Divider with "OR" text
        Row(
          children: [
            const Expanded(child: Divider()),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'OR',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Expanded(child: Divider()),
          ],
        ),
        const SizedBox(height: 20),

        // Facebook Login Button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1877F2), // Facebook blue
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: _signInWithFacebook,
            icon: const Icon(
              Icons.facebook,
              color: Colors.white,
              size: 20,
            ),
            label: const Text(
              'Continue with Facebook',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget get _navigateToRegister {
    return Padding(
      padding: EdgeInsets.only(top: 30),
      child: TextButton(
        onPressed: () {
          AppRoute.key.currentState?.pushNamed(AppRoute.registerScreen);
        },
        child: Text('dontHaveAccount'.tr),
      ),
    );
  }



  Widget get _loginButton {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.redAccent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onPressed: () {

          if (_formKey.currentState!.validate()) {
            String email = _emailController.text.trim();
            String password = _passwordController.text.trim();
            // Validate email and password with Firebase Auth
            _signIn(email, password);
          }
        },
        child: Text(
          'login'.tr,
          style: const TextStyle(fontSize: 16, color: Colors.white),
        ),
      ),
    );
  }

  Future<void> _signIn(String email, String password) async{
    try {
      UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password
      );

      if (userCredential.user != null) {
        // Save user data to SharedPreferences for AuthService
        final prefs = await SharedPreferences.getInstance();
        final username = userCredential.user!.displayName ??
                        userCredential.user!.email?.split('@')[0] ??
                        'User';

        await prefs.setString('username', username);
        await prefs.setString('email', userCredential.user!.email ?? '');
        await prefs.setString('password', password); // For AppSharedPref compatibility

        // Debug info (remove in production)
        // print('User signed in: ${userCredential.user?.email}');
        // print('Username saved: $username');

        // Small delay to ensure SharedPreferences are saved
        await Future.delayed(const Duration(milliseconds: 100));

        // Refresh UserController to update user state
        final userController = Get.find<UserController>();
        await userController.forceRefreshAfterLogin();

        // Navigate to main screen
        AppRoute.key.currentState?.pushReplacementNamed(AppRoute.mainScreen);
      }
    } catch (e) {
      // Handle error
      Get.snackbar(
        'Error',
        'Failed to sign in: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Sign in with Facebook
  Future<void> _signInWithFacebook() async {
    try {
      final userCredential = await FacebookAuthService.signInWithFacebook();

      if (userCredential != null) {
        // Force refresh the UserController to update the UI
        final userController = Get.find<UserController>();
        await userController.forceRefreshAfterLogin();

        // Show success message
        Get.snackbar(
          'Success',
          'Successfully logged in with Facebook!',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Navigate to main screen on successful login
        AppRoute.key.currentState?.pushReplacementNamed(AppRoute.mainScreen);
      }
    } catch (e) {
      // Error handling is already done in FacebookAuthService
      print('Facebook sign-in error: $e');
    }
  }
}
