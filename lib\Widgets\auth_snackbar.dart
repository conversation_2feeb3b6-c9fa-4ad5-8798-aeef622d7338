import 'package:flutter/material.dart';
import 'package:test_pro/utils/top_alert_utils.dart';

/// Custom SnackBar for authentication alerts
class AuthSnackBar {
  /// Show orange login required snackbar
  static void showLoginRequired(BuildContext context, {String? message}) {
    TopAlertUtils.showWarningAlert(
      context,
      message ?? 'Oops! You need to log in first to continue!',
      icon: Icons.warning_rounded,
      duration: const Duration(seconds: 4),
    );
  }

  /// Show success snackbar
  static void showSuccess(BuildContext context, String message) {
    TopAlertUtils.showSuccessAlert(context, message);
  }

  /// Show error snackbar
  static void showError(BuildContext context, String message) {
    TopAlertUtils.showErrorAlert(context, message);
  }
}
