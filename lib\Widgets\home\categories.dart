import 'package:flutter/material.dart';
import 'package:test_pro/Widgets/home/<USER>';
class CategoriesWidget extends StatefulWidget {
  final void Function(String)? onCategorySelected;

  const CategoriesWidget({super.key, this.onCategorySelected});

  @override
  State<CategoriesWidget> createState() => _CategoriesWidgetState();
}

class _CategoriesWidgetState extends State<CategoriesWidget> {
  final List<String> _categories = [
    "All",
    "Coffee",
    "Tea",
    "Bakery",
    "Snacks",
    "Desserts",
    "Drinks",
  ];

  String? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: _categories.map((category) {
          final bool isSelected = _selectedCategory == category;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategory = category;
              });
              if (widget.onCategorySelected != null) {
                widget.onCategorySelected!(category);
              }
            },
            child: SelectableCategory(
              category: category,
              isSelected: isSelected,
            ),
          );
        }).toList(),
      ),
    );
  }
}
