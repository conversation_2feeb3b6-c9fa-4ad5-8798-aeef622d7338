import 'package:flutter/material.dart';
import 'package:test_pro/models/outlet_model.dart';

class BuildMapMaker extends StatelessWidget {
  final Outlet outlet;
  final bool isSelected;
  final bool isFavorite;
  final VoidCallback? onTap;

  const BuildMapMaker({
    super.key,
    required this.outlet,
    this.isSelected = false,
    this.isFavorite = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.orange
                  : (outlet.isOperational ? Colors.green : Colors.grey),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.local_cafe,
              color: Colors.white,
              size: 20,
            ),
          ),

          // Favorite indicator
          if (isFavorite)
            Positioned(
              top: -2,
              right: -2,
              child: Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.favorite,
                  color: Colors.white,
                  size: 10,
                ),
              ),
            ),
        ],
      ),
    );
  }
}