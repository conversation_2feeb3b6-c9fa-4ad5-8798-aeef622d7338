import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/Widgets/home/<USER>';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/simple_cart_notifier.dart';
import 'package:test_pro/services/auth_service.dart';
import 'package:test_pro/screen/Card/cart_screen.dart';
import 'package:test_pro/theme/app_theme.dart';
import 'package:test_pro/controllers/user_controller.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SimpleCartMixin {
  final TextEditingController _searchController = TextEditingController();
  final UserController userController = Get.find<UserController>();

  String? selectedCategory;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }



  

  @override
  Widget build(BuildContext context) {
    List<Product> filteredProducts = selectedCategory == null
        ? allProducts
        : allProducts
              .where((product) => product.category == selectedCategory)
              .toList();

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: AppTheme.background,
        body: SingleChildScrollView(
          child: Column(
            children: [
              TopBar(),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.all(15),
                child: Column(
                  children: [
                    //Blog(),
                    //const SizedBox(height: 0),
                    LocateBranchHome(),
                    const SizedBox(height: 10),
                    CategoriesWidget(
                      onCategorySelected: (category) {
                        setState(() {
                          selectedCategory = category;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    ProductListWidget(products: filteredProducts),
                  ],
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: SimpleCartFAB(
          onPressed: () async {
            final navigator = Navigator.of(context);

            // Check authentication first
            final hasAuth = await AuthService.requireAuth(
              context,
              message: 'Please log in to access your cart.',
            );

            if (!hasAuth || !mounted) return;

            navigator.push(
              MaterialPageRoute(
                builder: (context) => const CartScreen(),
              ),
            ).then((_) {
              // Refresh cart when returning from cart screen
              if (mounted) {
                refreshCart();
              }
            });
          },
          backgroundColor: const Color.fromARGB(255, 255, 86, 86),
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  
}
