import 'package:flutter/material.dart';
import 'package:test_pro/screen/product_detail_screen.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/favorite_service.dart';
import 'package:test_pro/services/simple_cart_notifier.dart';
import 'package:test_pro/services/auth_service.dart';

class BuildProductItem extends StatefulWidget {
  final Product product;

  const BuildProductItem({
    super.key,
    required this.product,
  });

  @override
  State<BuildProductItem> createState() => _BuildProductItemState();
}

class _BuildProductItemState extends State<BuildProductItem> {
  Future<void> _addToCart() async {
    // Check authentication first
    final hasAuth = await AuthService.requireAuth(
      context,
      message: 'Please log in to add items to your cart.',
    );

    if (!hasAuth) return;

    final cartNotifier = SimpleCartNotifier();
    await cartNotifier.addToCart(
      product: widget.product,
      quantity: 1,
      temperature: "Cold",
      size: "Medium",
      sweetness: "Normal",
      topping: "Normal",
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Added ${widget.product.name} to cart!'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final navigator = Navigator.of(context);

        // Allow guest users to view product details
        // Authentication will be checked inside ProductDetailScreen for specific actions
        if (!mounted) return;

        navigator.push(
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(product: widget.product),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image and favorite button
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      child: widget.product.image != null
                          ? Image.asset(
                              widget.product.image!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(
                                  child: Icon(
                                    Icons.coffee,
                                    color: Colors.brown,
                                    size: 40,
                                  ),
                                );
                              },
                            )
                          : const Center(
                              child: Icon(
                                Icons.coffee,
                                color: Colors.brown,
                                size: 40,
                              ),
                            ),
                    ),
                  ),
                  // Favorite button
                  Positioned(
                    top: 8,
                    left: 8,
                    child: FutureBuilder<bool>(
                      future: FavoriteService.isFavorite(widget.product.id ?? 0),
                      builder: (context, snapshot) {
                        final isFavorite = snapshot.data ?? false;
                        return GestureDetector(
                          onTap: () async {
                            // Check authentication first
                            final hasAuth = await AuthService.requireAuth(
                              context,
                              message: 'Please log in to add items to your favorites.',
                            );

                            if (!hasAuth) return;

                            await FavoriteService.toggleFavorite(widget.product);
                            setState(() {}); // Refresh the UI
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Theme.of(context).cardColor.withValues(alpha: 0.9),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              isFavorite ? Icons.favorite : Icons.favorite_border,
                              color: isFavorite ? Colors.red : Theme.of(context).iconTheme.color,
                              size: 20,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            // Product details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.product.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.titleLarge?.color,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (widget.product.description != null)
                      Text(
                        widget.product.description!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    const Spacer(),
                    // Price and add button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '\$${widget.product.price.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        GestureDetector(
                          onTap: _addToCart,
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.add,
                              color: Theme.of(context).colorScheme.onPrimary,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}