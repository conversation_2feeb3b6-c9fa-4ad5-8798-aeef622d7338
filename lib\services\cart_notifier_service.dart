import 'dart:async';
import 'package:flutter/material.dart';
import 'package:test_pro/services/cart_service.dart';

class CartNotifierService extends ChangeNotifier {
  static final CartNotifierService _instance = CartNotifierService._internal();
  factory CartNotifierService() => _instance;
  CartNotifierService._internal();

  int _cartItemCount = 0;
  double _cartTotalPrice = 0.0;
  bool _isLoading = false;
  bool _isInitialized = false;

  int get cartItemCount => _cartItemCount;
  double get cartTotalPrice => _cartTotalPrice;
  bool get isLoading => _isLoading;
  bool get hasItems => _cartItemCount > 0;
  bool get isInitialized => _isInitialized;

  // Initialize cart data
  Future<void> initialize() async {
    if (_isInitialized) return;
    await _updateCartData();
    _isInitialized = true;
  }

  // Update cart data from storage
  Future<void> _updateCartData() async {
    final wasLoading = _isLoading;
    _isLoading = true;

    // Only notify if loading state changed
    if (!wasLoading) {
      _safeNotifyListeners();
    }

    try {
      final count = await CartService.getCartItemCount();
      final total = await CartService.getTotalPrice();

      final countChanged = _cartItemCount != count;
      final totalChanged = (_cartTotalPrice - total).abs() > 0.01;

      _cartItemCount = count;
      _cartTotalPrice = total;

      // Only notify if values actually changed
      if (countChanged || totalChanged) {
        _safeNotifyListeners();
      }
    } catch (e) {
      _cartItemCount = 0;
      _cartTotalPrice = 0.0;
      _safeNotifyListeners();
    }

    _isLoading = false;
    _safeNotifyListeners();
  }

  // Safe notification that avoids setState during build
  void _safeNotifyListeners() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // Add item to cart and update
  Future<void> addToCart({
    required product,
    required int quantity,
    String? temperature,
    String? size,
    String? sweetness,
    String? topping,
  }) async {
    await CartService.addToCart(
      product: product,
      quantity: quantity,
      temperature: temperature,
      size: size,
      sweetness: sweetness,
      topping: topping,
    );
    await _updateCartData();
  }

  // Update quantity and refresh
  Future<void> updateQuantity(int productId, int newQuantity) async {
    await CartService.updateQuantity(productId, newQuantity);
    await _updateCartData();
  }

  // Remove item and refresh
  Future<void> removeFromCart(int productId) async {
    await CartService.removeFromCart(productId);
    await _updateCartData();
  }

  // Clear cart and refresh
  Future<void> clearCart() async {
    await CartService.clearCart();
    await _updateCartData();
  }

  // Refresh cart data (useful when returning from cart screen)
  Future<void> refresh() async {
    await _updateCartData();
  }

  // Force immediate update (use sparingly)
  void forceUpdate() {
    _safeNotifyListeners();
  }
}

// Mixin for widgets that need cart functionality
mixin CartMixin<T extends StatefulWidget> on State<T> {
  late CartNotifierService _cartNotifier;

  @override
  void initState() {
    super.initState();
    _cartNotifier = CartNotifierService();
    _cartNotifier.addListener(_onCartChanged);
    // Initialize after the first frame to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _cartNotifier.initialize();
      }
    });
  }

  @override
  void dispose() {
    _cartNotifier.removeListener(_onCartChanged);
    super.dispose();
  }

  void _onCartChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  // Getters for easy access
  int get cartItemCount => _cartNotifier.cartItemCount;
  double get cartTotalPrice => _cartNotifier.cartTotalPrice;
  bool get hasCartItems => _cartNotifier.hasItems;
  bool get isCartLoading => _cartNotifier.isLoading;

  // Methods for cart operations
  Future<void> addToCart({
    required product,
    required int quantity,
    String? temperature,
    String? size,
    String? sweetness,
    String? topping,
  }) async {
    await _cartNotifier.addToCart(
      product: product,
      quantity: quantity,
      temperature: temperature,
      size: size,
      sweetness: sweetness,
      topping: topping,
    );
  }

  Future<void> updateCartQuantity(int productId, int newQuantity) async {
    await _cartNotifier.updateQuantity(productId, newQuantity);
  }

  Future<void> removeFromCart(int productId) async {
    await _cartNotifier.removeFromCart(productId);
  }

  Future<void> clearCart() async {
    await _cartNotifier.clearCart();
  }

  Future<void> refreshCart() async {
    await _cartNotifier.refresh();
  }
}

// Widget for displaying cart count badge
class CartCountBadge extends StatelessWidget {
  final Widget child;
  final int count;
  final Color? badgeColor;
  final Color? textColor;
  final double? badgeSize;

  const CartCountBadge({
    super.key,
    required this.child,
    required this.count,
    this.badgeColor,
    this.textColor,
    this.badgeSize,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        if (count > 0)
          Positioned(
            right: -8,
            top: -8,
            child: Container(
              padding: EdgeInsets.all(badgeSize != null ? badgeSize! / 4 : 4),
              decoration: BoxDecoration(
                color: badgeColor ?? Colors.red,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1),
              ),
              constraints: BoxConstraints(
                minWidth: badgeSize ?? 20,
                minHeight: badgeSize ?? 20,
              ),
              child: Text(
                count > 99 ? '99+' : count.toString(),
                style: TextStyle(
                  color: textColor ?? Colors.white,
                  fontSize: (badgeSize != null ? badgeSize! / 2.5 : 10),
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}

// Animated cart floating action button
class AnimatedCartFAB extends StatefulWidget {
  final VoidCallback onPressed;
  final int itemCount;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? heroTag;

  const AnimatedCartFAB({
    super.key,
    required this.onPressed,
    required this.itemCount,
    this.backgroundColor,
    this.foregroundColor,
    this.heroTag,
  });

  @override
  State<AnimatedCartFAB> createState() => _AnimatedCartFABState();
}

class _AnimatedCartFABState extends State<AnimatedCartFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.itemCount > 0) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedCartFAB oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.itemCount > 0 && oldWidget.itemCount == 0) {
      _animationController.forward();
    } else if (widget.itemCount == 0 && oldWidget.itemCount > 0) {
      _animationController.reverse();
    } else if (widget.itemCount != oldWidget.itemCount && widget.itemCount > 0) {
      // Bounce animation when count changes
      _animationController.forward().then((_) {
        _animationController.reverse().then((_) {
          _animationController.forward();
        });
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.itemCount == 0) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: FloatingActionButton(
              heroTag: widget.heroTag ?? "cart_fab",
              onPressed: widget.onPressed,
              backgroundColor: widget.backgroundColor ?? Colors.red,
              foregroundColor: widget.foregroundColor ?? Colors.white,
              child: CartCountBadge(
                count: widget.itemCount,
                badgeColor: Colors.white,
                textColor: Colors.red,
                badgeSize: 24,
                child: const Icon(
                  Icons.shopping_cart,
                  size: 28,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
