import 'package:flutter/material.dart';

class EVallets extends StatefulWidget {
  const EVallets({super.key});

  @override
  State<EVallets> createState() => _EValletsState();
}

class _EValletsState extends State<EVallets> {
  String selectedBank = 'Choose Bank';

  final List<String> banks = [
    'PayPal',
    'Google Pay',
    'Apple Pay',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Bank Transfer')),
      backgroundColor: Colors.grey[200],
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Fixed-size box with selection
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text("Selected Bank:", style: TextStyle(fontSize: 16)),
                const SizedBox(height: 8),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    setState(() {
                      selectedBank = value;
                    });
                  },
                  itemBuilder: (context) => banks
                      .map((bank) => PopupMenuItem(
                            value: bank,
                            child: Text(bank),
                          ))
                      .toList(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(selectedBank,
                            style: const TextStyle(fontSize: 16)),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
