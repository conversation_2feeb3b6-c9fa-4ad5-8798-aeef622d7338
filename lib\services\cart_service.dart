import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:test_pro/models/cart_model.dart';
import 'package:test_pro/models/home/<USER>';

class CartService {
  static Future<String> _getFilePath() async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}/cart.json';
  }

  static Future<List<CartItem>> getCartItems() async {
    final path = await _getFilePath();
    final file = File(path);

    if (!(await file.exists())) return [];

    final lines = await file.readAsLines();
    return lines
        .where((line) => line.trim().isNotEmpty)
        .map((line) {
          try {
            return CartItem.fromMap(jsonDecode(line));
          } catch (e) {
            // Log error in production, for now just skip invalid items
            return null;
          }
        })
        .whereType<CartItem>()
        .toList();
  }

  static Future<void> _saveCart(List<CartItem> cart) async {
    final path = await _getFilePath();
    final file = File(path);
    final sink = file.openWrite();
    for (final item in cart) {
      sink.writeln(jsonEncode(item.toMap()));
    }
    await sink.close();
  }

  static Future<void> addToCart({
    required Product product,
    required int quantity,
    String? temperature,
    String? size,
    String? sweetness,
    String? topping,
  }) async {
    final cart = await getCartItems();
    final productId = product.id ?? 0;

    final existingIndex = cart.indexWhere((item) =>
        item.productId == productId &&
        item.temperature == temperature &&
        item.size == size &&
        item.sweetness == sweetness &&
        item.topping == topping);

    if (existingIndex != -1) {
      cart[existingIndex].quantity = cart[existingIndex].quantity + quantity;
    } else {
      final cartItem = CartItem(
        productId: productId,
        name: product.name,
        category: product.category,
        price: product.price,
        quantity: quantity,
        image: product.image,
        description: product.description,
        temperature: temperature,
        size: size,
        sweetness: sweetness,
        topping: topping,
      );
      cart.add(cartItem);
    }

    await _saveCart(cart);
  }

  static Future<void> updateQuantity(int productId, int newQuantity) async {
    final cart = await getCartItems();
    final index = cart.indexWhere((item) => item.productId == productId);

    if (index != -1) {
      if (newQuantity <= 0) {
        cart.removeAt(index);
      } else {
        cart[index].quantity = newQuantity;
      }
      await _saveCart(cart);
    }
  }

  static Future<void> removeFromCart(int productId) async {
    final cart = await getCartItems();
    cart.removeWhere((item) => item.productId == productId);
    await _saveCart(cart);
  }

  static Future<void> clearCart() async {
    final path = await _getFilePath();
    final file = File(path);
    if (await file.exists()) {
      await file.delete();
    }
  }

  static Future<double> getTotalPrice() async {
    final cart = await getCartItems();
    return cart.fold<double>(0.0, (sum, item) => sum + item.totalPrice);
  }

  static Future<int> getCartItemCount() async {
    final cart = await getCartItems();
    return cart.fold<int>(0, (sum, item) => sum + item.quantity);
  }

  static Future<int> getCartItemsLength() async {
    final cart = await getCartItems();
    return cart.length;
  }
}
