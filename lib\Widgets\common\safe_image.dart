import 'package:flutter/material.dart';

class SafeImage extends StatelessWidget {
  final String? imagePath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const SafeImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    // Default fallback images for different categories
    final Map<String, String> fallbackImages = {
      'coffee': 'assets/images/coffee/espresso.png',
      'tea': 'assets/images/coffee/espresso.png',
      'default': 'assets/images/coffee/espresso.png',
    };

    Widget buildErrorWidget() {
      if (errorWidget != null) return errorWidget!;
      
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: borderRadius,
        ),
        child: const Center(
          child: Icon(
            Icons.coffee,
            color: Colors.brown,
            size: 40,
          ),
        ),
      );
    }



    // If no image path provided, show error widget
    if (imagePath == null || imagePath!.isEmpty) {
      return buildErrorWidget();
    }

    Widget imageWidget = Image.asset(
      imagePath!,
      width: width,
      height: height,
      fit: fit,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) return child;
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
          child: child,
        );
      },
      errorBuilder: (context, error, stackTrace) {
        // Try to determine category from image path for better fallback
        String category = 'default';
        if (imagePath!.contains('coffee')) category = 'coffee';
        if (imagePath!.contains('tea')) category = 'tea';
        
        final fallbackPath = fallbackImages[category]!;
        
        // Try fallback image
        return Image.asset(
          fallbackPath,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) {
            // If even fallback fails, show icon
            return buildErrorWidget();
          },
        );
      },
    );

    // Apply border radius if specified
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

// Convenience constructors for common use cases
class SafeProductImage extends StatelessWidget {
  final String? imagePath;
  final double size;

  const SafeProductImage({
    super.key,
    required this.imagePath,
    this.size = 100,
  });

  @override
  Widget build(BuildContext context) {
    return SafeImage(
      imagePath: imagePath,
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(12),
      fit: BoxFit.cover,
    );
  }
}

class SafeProductThumbnail extends StatelessWidget {
  final String? imagePath;
  final double size;

  const SafeProductThumbnail({
    super.key,
    required this.imagePath,
    this.size = 60,
  });

  @override
  Widget build(BuildContext context) {
    return SafeImage(
      imagePath: imagePath,
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(8),
      fit: BoxFit.cover,
    );
  }
}

class SafeProductCard extends StatelessWidget {
  final String? imagePath;
  final double width;
  final double height;

  const SafeProductCard({
    super.key,
    required this.imagePath,
    this.width = double.infinity,
    this.height = 150,
  });

  @override
  Widget build(BuildContext context) {
    return SafeImage(
      imagePath: imagePath,
      width: width,
      height: height,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
      ),
      fit: BoxFit.cover,
    );
  }
}
