import 'package:flutter/material.dart';

class BankTransfer extends StatefulWidget {
  const BankTransfer({super.key});

  @override
  State<BankTransfer> createState() => _BankTransferState();
}

class _BankTransferState extends State<BankTransfer> {
  int? expandedIndex;
  final Map<int, String> selectedOptions =
      {}; // Track selection by section index

  final List<Map<String, dynamic>> paymentMethods = [
    {
      'title': 'E - Wallet',
      'icon': Icons.account_balance_wallet_outlined,
      'options': [
        {'name': 'Paypal', 'icon': Icons.paypal},
        {'name': 'Google Pay', 'icon': Icons.g_mobiledata},
        {'name': 'Apple Pay', 'icon': Icons.apple},
      ],
    },
    {
      'title': 'Bank Transfer',
      'icon': Icons.swap_horiz,
      'options': [
        {'name': 'ABA Bank', 'icon': Icons.account_balance},
        {'name': 'ACLEDA Bank', 'icon': Icons.account_balance},
        {'name': 'Canadia Bank', 'icon': Icons.account_balance},
      ],
    },
    {
      'title': 'Debit / Credit Card',
      'icon': Icons.credit_card,
      'options': [
        {'name': 'Visa', 'icon': Icons.credit_card},
        {'name': 'MasterCard', 'icon': Icons.credit_card},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Method Payment'),
        backgroundColor: const Color(0xFFF4F4F2),
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      backgroundColor: const Color(0xFFF4F4F2),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: paymentMethods.length,
        itemBuilder: (context, index) {
          final method = paymentMethods[index];
          final isExpanded = expandedIndex == index;
          final selected = selectedOptions[index];

          return Column(
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    expandedIndex = isExpanded ? null : index;
                  });

                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(isExpanded ? 12 : 12),
                  ),
                  child: Row(
                    children: [
                      Icon(method['icon'], size: 20),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              method['title'],
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (selected != null)
                              Text(
                                selected,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                        ),
                      ),
                      Icon(isExpanded ? Icons.expand_less : Icons.expand_more),
                    ],
                  ),
                ),
              ),
              if (isExpanded)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(12),
                    ),
                  ),
                  child: Column(
                    children: (method['options'] as List)
                        .map(
                          (option) => ListTile(
                            contentPadding: EdgeInsets.zero,
                            leading: Icon(option['icon']),
                            title: Text(option['name']),
                            onTap: () {
                              setState(() {
                                selectedOptions[index] = option['name'];
                                expandedIndex =
                                    null; // collapse after selection
                              });
                            },
                          ),
                        )
                        .toList(),
                  ),
                ),
              const SizedBox(height: 12),
            ],
          );
        },
      ),
    );
  }
}
