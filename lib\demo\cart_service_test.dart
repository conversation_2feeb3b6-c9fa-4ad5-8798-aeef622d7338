import 'package:flutter/material.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/services/cart_service.dart';
import 'package:test_pro/services/cart_notifier_service.dart';

class CartServiceTest extends StatefulWidget {
  const CartServiceTest({super.key});

  @override
  State<CartServiceTest> createState() => _CartServiceTestState();
}

class _CartServiceTestState extends State<CartServiceTest> {
  String _testResults = '';
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cart Service Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cart Service Functionality Test',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: _isRunning ? null : _runTests,
              child: _isRunning 
                  ? const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Running Tests...'),
                      ],
                    )
                  : const Text('Run Cart Tests'),
            ),
            
            const SizedBox(height: 16),
            
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults.isEmpty ? 'Click "Run Cart Tests" to start testing...' : _testResults,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = '';
    });

    await _addTestResult('🧪 Starting Cart Service Tests...\n');

    try {
      // Test 1: Clear cart first
      await _addTestResult('Test 1: Clearing cart...');
      await CartService.clearCart();
      final initialCart = await CartService.getCartItems();
      await _addTestResult('✅ Cart cleared. Items: ${initialCart.length}\n');

      // Test 2: Add first product
      await _addTestResult('Test 2: Adding first product...');
      final product1 = allProducts.first;
      await CartService.addToCart(
        product: product1,
        quantity: 2,
        temperature: 'Hot',
        size: 'Large',
        sweetness: 'Sweet',
        topping: 'Extra Foam',
      );
      
      final cart1 = await CartService.getCartItems();
      final count1 = await CartService.getCartItemCount();
      final total1 = await CartService.getTotalPrice();
      
      await _addTestResult('✅ Added ${product1.name} x2');
      await _addTestResult('   Cart items: ${cart1.length}');
      await _addTestResult('   Total count: $count1');
      await _addTestResult('   Total price: \$${total1.toStringAsFixed(2)}\n');

      // Test 3: Add second product
      await _addTestResult('Test 3: Adding second product...');
      final product2 = allProducts[1];
      await CartService.addToCart(
        product: product2,
        quantity: 1,
        temperature: 'Cold',
        size: 'Medium',
        sweetness: 'Normal',
        topping: 'None',
      );
      
      final cart2 = await CartService.getCartItems();
      final count2 = await CartService.getCartItemCount();
      final total2 = await CartService.getTotalPrice();
      
      await _addTestResult('✅ Added ${product2.name} x1');
      await _addTestResult('   Cart items: ${cart2.length}');
      await _addTestResult('   Total count: $count2');
      await _addTestResult('   Total price: \$${total2.toStringAsFixed(2)}\n');

      // Test 4: Add same product with different options
      await _addTestResult('Test 4: Adding same product with different options...');
      await CartService.addToCart(
        product: product1,
        quantity: 1,
        temperature: 'Cold', // Different temperature
        size: 'Small',       // Different size
        sweetness: 'Normal',  // Different sweetness
        topping: 'None',     // Different topping
      );
      
      final cart3 = await CartService.getCartItems();
      final count3 = await CartService.getCartItemCount();
      final total3 = await CartService.getTotalPrice();
      
      await _addTestResult('✅ Added ${product1.name} x1 (different options)');
      await _addTestResult('   Cart items: ${cart3.length}');
      await _addTestResult('   Total count: $count3');
      await _addTestResult('   Total price: \$${total3.toStringAsFixed(2)}\n');

      // Test 5: Add same product with same options (should increase quantity)
      await _addTestResult('Test 5: Adding same product with same options...');
      await CartService.addToCart(
        product: product1,
        quantity: 1,
        temperature: 'Hot',
        size: 'Large',
        sweetness: 'Sweet',
        topping: 'Extra Foam',
      );
      
      final cart4 = await CartService.getCartItems();
      final count4 = await CartService.getCartItemCount();
      final total4 = await CartService.getTotalPrice();
      
      await _addTestResult('✅ Added ${product1.name} x1 (same options - should merge)');
      await _addTestResult('   Cart items: ${cart4.length}');
      await _addTestResult('   Total count: $count4');
      await _addTestResult('   Total price: \$${total4.toStringAsFixed(2)}\n');

      // Test 6: Update quantity
      await _addTestResult('Test 6: Updating quantity...');
      final firstItem = cart4.first;
      await CartService.updateQuantity(firstItem.productId, 5);
      
      final cart5 = await CartService.getCartItems();
      final count5 = await CartService.getCartItemCount();
      final total5 = await CartService.getTotalPrice();
      
      await _addTestResult('✅ Updated quantity to 5');
      await _addTestResult('   Cart items: ${cart5.length}');
      await _addTestResult('   Total count: $count5');
      await _addTestResult('   Total price: \$${total5.toStringAsFixed(2)}\n');

      // Test 7: Remove item
      await _addTestResult('Test 7: Removing item...');
      await CartService.removeFromCart(firstItem.productId);
      
      final cart6 = await CartService.getCartItems();
      final count6 = await CartService.getCartItemCount();
      final total6 = await CartService.getTotalPrice();
      
      await _addTestResult('✅ Removed item');
      await _addTestResult('   Cart items: ${cart6.length}');
      await _addTestResult('   Total count: $count6');
      await _addTestResult('   Total price: \$${total6.toStringAsFixed(2)}\n');

      // Test 8: Test CartNotifierService
      await _addTestResult('Test 8: Testing CartNotifierService...');
      final cartNotifier = CartNotifierService();
      await cartNotifier.initialize();
      
      await _addTestResult('✅ CartNotifierService initialized');
      await _addTestResult('   Notifier count: ${cartNotifier.cartItemCount}');
      await _addTestResult('   Notifier total: \$${cartNotifier.cartTotalPrice.toStringAsFixed(2)}');
      await _addTestResult('   Has items: ${cartNotifier.hasItems}\n');

      // Test 9: Add via notifier
      await _addTestResult('Test 9: Adding via CartNotifierService...');
      await cartNotifier.addToCart(
        product: allProducts[2],
        quantity: 2,
        temperature: 'Hot',
        size: 'Large',
        sweetness: 'Sweet',
        topping: 'Whipped Cream',
      );
      
      await _addTestResult('✅ Added via notifier');
      await _addTestResult('   Notifier count: ${cartNotifier.cartItemCount}');
      await _addTestResult('   Notifier total: \$${cartNotifier.cartTotalPrice.toStringAsFixed(2)}');
      await _addTestResult('   Has items: ${cartNotifier.hasItems}\n');

      // Test 10: Final verification
      await _addTestResult('Test 10: Final verification...');
      final finalCart = await CartService.getCartItems();
      final finalCount = await CartService.getCartItemCount();
      final finalTotal = await CartService.getTotalPrice();
      
      await _addTestResult('✅ Final cart state:');
      await _addTestResult('   Direct service - Items: ${finalCart.length}, Count: $finalCount, Total: \$${finalTotal.toStringAsFixed(2)}');
      await _addTestResult('   Notifier service - Count: ${cartNotifier.cartItemCount}, Total: \$${cartNotifier.cartTotalPrice.toStringAsFixed(2)}\n');

      // Verify consistency
      if (finalCount == cartNotifier.cartItemCount && 
          (finalTotal - cartNotifier.cartTotalPrice).abs() < 0.01) {
        await _addTestResult('✅ Services are consistent!\n');
      } else {
        await _addTestResult('❌ Services are inconsistent!\n');
      }

      await _addTestResult('🎉 All tests completed successfully!');

    } catch (e) {
      await _addTestResult('❌ Test failed with error: $e');
    }

    setState(() {
      _isRunning = false;
    });
  }

  Future<void> _addTestResult(String result) async {
    setState(() {
      _testResults += '$result\n';
    });
    // Small delay to make the UI update visible
    await Future.delayed(const Duration(milliseconds: 100));
  }
}
