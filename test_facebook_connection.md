# Test Facebook Firebase Connection

## Quick Connection Test

### 1. Update strings.xml
Replace the placeholders in `android/app/src/main/res/values/strings.xml`:

```xml
<string name="facebook_app_id">YOUR_ACTUAL_APP_ID</string>
<string name="facebook_client_token">YOUR_ACTUAL_CLIENT_TOKEN</string>
<string name="fb_login_protocol_scheme">fbYOUR_ACTUAL_APP_ID</string>
```

### 2. Firebase Console Setup
1. Go to Firebase Console → Authentication → Sign-in method
2. Enable Facebook provider
3. Add your Facebook App ID and App Secret
4. Copy the OAuth redirect URI
5. Add this URI to your Facebook app settings

### 3. Test the Login
1. Run: `flutter run`
2. Go to login screen
3. Click "Continue with Facebook"
4. Should open Facebook login
5. After login, should return to app

### 4. Check for Errors
If login fails, check:
- Android logs: `adb logcat | grep -i facebook`
- Firebase console for authentication events
- Facebook app is in "Live" mode (not Development)

### 5. Common Issues & Solutions

**"Invalid key hash"**
- Regenerate key hash with correct command
- Make sure using debug keystore for development

**"App not configured for Facebook Login"**
- Check package name matches exactly: `com.example.coffe_vortex`
- Verify Facebook Login product is added to your app

**"OAuth redirect URI mismatch"**
- Copy exact URI from Firebase (including https://)
- No trailing slashes or extra characters

**"Login cancelled immediately"**
- Check Facebook app is published/live
- Verify all configuration steps completed

### 6. Verification Steps

✅ Facebook App ID and Client Token added to strings.xml
✅ Firebase Facebook provider enabled with App ID/Secret
✅ OAuth redirect URI added to Facebook app
✅ Android platform configured in Facebook app
✅ Key hash generated and added to Facebook app
✅ App tested on real device (not emulator for best results)

### 7. Debug Commands

```bash
# View app logs
flutter logs

# View Facebook-specific logs
adb logcat | grep -i facebook

# Clean and rebuild
flutter clean
flutter pub get
flutter run
```

### 8. Success Indicators

When working correctly:
- Facebook login button appears on login screen
- Clicking opens Facebook authentication
- User can complete login flow
- App receives user data and navigates to main screen
- User profile shows Facebook information
- Logout properly signs out of both Facebook and Firebase
