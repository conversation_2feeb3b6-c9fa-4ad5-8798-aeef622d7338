import 'package:flutter/material.dart';

class PaymentSection extends StatefulWidget {
  const PaymentSection({super.key});

  @override
  State<PaymentSection> createState() => _PaymentSectionState();
}

class _PaymentSectionState extends State<PaymentSection> {

  @override
  Widget build(BuildContext context) {
    // Return empty container since payment method selection is removed
    return const SizedBox.shrink();
  }
}
