class CartItem {
  final int productId;
  final String name;
  final String category;
  final double price;
  int quantity;
  final String? image;
  final String? description;
  final String? temperature;
  final String? size;
  final String? sweetness;
  final String? topping;

  CartItem({
    required this.productId,
    required this.name,
    required this.category,
    required this.price,
    required this.quantity,
    this.image,
    this.description,
    this.temperature,
    this.size,
    this.sweetness,
    this.topping,
  });

  double get totalPrice => price * quantity;

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'name': name,
      'category': category,
      'price': price,
      'quantity': quantity,
      'image': image,
      'description': description,
      'temperature': temperature,
      'size': size,
      'sweetness': sweetness,
      'topping': topping,
    };
  }

  factory CartItem.fromMap(Map<String, dynamic> map) {
    return CartItem(
      productId: map['productId'] ?? 0,
      name: map['name'] ?? '',
      category: map['category'] ?? '',
      price: (map['price'] is int ? (map['price'] as int).toDouble() : map['price']) ?? 0.0,
      quantity: map['quantity'] ?? 1,
      image: map['image'],
      description: map['description'],
      temperature: map['temperature'],
      size: map['size'],
      sweetness: map['sweetness'],
      topping: map['topping'],
    );
  }
}
