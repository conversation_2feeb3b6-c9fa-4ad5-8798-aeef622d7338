import 'package:flutter/material.dart';
import 'package:test_pro/models/home/<USER>';

class ProductImage extends StatelessWidget {
  final Product product;
  const ProductImage({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 200,
        height: 200,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(25),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: product.image != null
              ? Image.asset(
                  product.image!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(
                      Icons.coffee,
                      size: 80,
                      color: Colors.brown,
                    );
                  },
                )
              : const Icon(
                  Icons.coffee,
                  size: 80,
                  color: Colors.brown,
                ),
        ),
      ),
    );
  }
}