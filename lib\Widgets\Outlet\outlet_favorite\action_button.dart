import 'package:flutter/material.dart';
import 'package:test_pro/models/outlet_model.dart';

class ActionButton extends StatelessWidget {
  final Outlet outlet;

  const ActionButton({
    super.key,
    required this.outlet,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // TODO: Get directions
                        },
                        icon: const Icon(Icons.directions, size: 16),
                        label: const Text('Directions'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.brown,
                          side: const BorderSide(color: Colors.brown),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (outlet.phone != null)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // TODO: Make phone call
                          },
                          icon: const Icon(Icons.phone, size: 16),
                          label: const Text('Call'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.brown,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                  ],
                );
  }
}