import 'package:flutter/material.dart';
import 'package:test_pro/models/cart_model.dart';
import 'package:test_pro/Widgets/card_checkout/card_item.dart';

class ItemSection extends StatelessWidget {
  final List<CartItem> cartItems;
  final Function(int, int)? onUpdateQuantity;
  final VoidCallback? onAddMore;

  const ItemSection({
    super.key,
    required this.cartItems,
    this.onUpdateQuantity,
    this.onAddMore,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Items',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: onAddMore,
                child: const Text('add more →'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...cartItems.map((item) => CardItem(
            item: item,
            onUpdateQuantity: onUpdateQuantity,
          )),
        ],
      ),
    );
  }
}