import 'package:flutter/material.dart';
import 'package:test_pro/models/home/<USER>';
import 'package:test_pro/Widgets/search/build_product_item.dart';

class SearchResult extends StatefulWidget {
  final List<Product> filteredProducts;
  final List<String> recentSearches;
  final TextEditingController searchController;
  final Function(String) onSearchTap;
  final bool showRecentSearches;

  const SearchResult({
    super.key,
    required this.filteredProducts,
    required this.recentSearches,
    required this.searchController,
    required this.onSearchTap,
    this.showRecentSearches = true,
  });

  @override
  State<SearchResult> createState() => _SearchResultState();
}

class _SearchResultState extends State<SearchResult> {
  String _selectedCategory = "All";
  List<Product> _displayedProducts = [];

  final List<String> _categories = [
    "All",
    "Coffee",
    "Non-Coffee",
    "Tea",
  ];

  @override
  void initState() {
    super.initState();
    _updateDisplayedProducts();
  }

  @override
  void didUpdateWidget(SearchResult oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filteredProducts != widget.filteredProducts) {
      _updateDisplayedProducts();
    }
  }

  void _updateDisplayedProducts() {
    if (_selectedCategory == "All") {
      _displayedProducts = widget.filteredProducts;
    } else {
      String categoryFilter = _selectedCategory;
      if (_selectedCategory == "Non-Coffee") {
        // Non-Coffee includes everything except Coffee
        _displayedProducts = widget.filteredProducts
            .where((product) => product.category.toLowerCase() != "coffee")
            .toList();
      } else {
        _displayedProducts = widget.filteredProducts
            .where((product) => product.category.toLowerCase() == categoryFilter.toLowerCase())
            .toList();
      }
    }
  }

  void _onCategorySelected(String category) {
    setState(() {
      _selectedCategory = category;
      _updateDisplayedProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Show recent searches section when search is empty
        if (widget.showRecentSearches && widget.searchController.text.isEmpty && widget.recentSearches.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).scaffoldBackgroundColor,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Recent Searches',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.titleLarge?.color,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: widget.recentSearches.take(5).map((search) {
                    return GestureDetector(
                      onTap: () => widget.onSearchTap(search),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Theme.of(context).dividerColor),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.history, size: 14, color: Theme.of(context).iconTheme.color),
                            const SizedBox(width: 4),
                            Text(
                              search,
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).textTheme.bodyMedium?.color,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),

        // Category filter chips
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _categories.map((category) {
                final isSelected = _selectedCategory == category;
                return GestureDetector(
                  onTap: () => _onCategorySelected(category),
                  child: Container(
                    margin: const EdgeInsets.only(right: 12),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(20),
                      border: isSelected
                          ? null
                          : Border.all(color: Theme.of(context).dividerColor),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (category == "All")
                          Icon(
                            Icons.apps,
                            size: 16,
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).iconTheme.color,
                          )
                        else if (category == "Coffee")
                          Icon(
                            Icons.coffee,
                            size: 16,
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).iconTheme.color,
                          )
                        else if (category == "Non-Coffee")
                          Icon(
                            Icons.local_dining,
                            size: 16,
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).iconTheme.color,
                          )
                        else if (category == "Tea")
                          Icon(
                            Icons.emoji_food_beverage,
                            size: 16,
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).iconTheme.color,
                          ),
                        const SizedBox(width: 6),
                        Text(
                          category,
                          style: TextStyle(
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).textTheme.bodyMedium?.color,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),

        // Products grid
        Expanded(
          child: _displayedProducts.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No products found',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: _displayedProducts.length,
                  itemBuilder: (context, index) {
                    final product = _displayedProducts[index];
                    return BuildProductItem(product: product);
                  },
                ),
        ),
      ],
    );
  }
}