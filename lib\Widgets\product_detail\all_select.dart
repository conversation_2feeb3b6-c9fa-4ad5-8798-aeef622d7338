import 'package:flutter/material.dart';

class AllSelect extends StatefulWidget {
  const AllSelect({super.key});

  @override
  State<AllSelect> createState() => _AllSelectState();
}

class _AllSelectState extends State<AllSelect> {
  String selectedTemperature = "Cold";

  String selectedSize = "Medium";

  String selectedSweetness = "Normal";

  String selectedTopping = "Normal";

  int quantity = 1;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Select Cold or Hot",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildOptionButton("Cold", selectedTemperature == "Cold", () {
              setState(() {
                selectedTemperature = "Cold";
              });
            }),
            const SizedBox(width: 12),
            _buildOptionButton("Hot", selectedTemperature == "Hot", () {
              setState(() {
                selectedTemperature = "Hot";
              });
            }),
          ],
        ),
        const SizedBox(height: 24),

        const Text(
          "Cup Size",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildOptionButton("Small", selectedSize == "Small", () {
              setState(() {
                selectedSize = "Small";
              });
            }),
            const SizedBox(width: 12),
            _buildOptionButton("Medium", selectedSize == "Medium", () {
              setState(() {
                selectedSize = "Medium";
              });
            }),
            const SizedBox(width: 12),
            _buildOptionButton("Large", selectedSize == "Large", () {
              setState(() {
                selectedSize = "Large";
              });
            }),
          ],
        ),
        const SizedBox(height: 24),

        const Text(
          "Sweetness",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildOptionButton("Normal", selectedSweetness == "Normal", () {
              setState(() {
                selectedSweetness = "Normal";
              });
            }),
            const SizedBox(width: 12),
            _buildOptionButton(
              "Less Sweet",
              selectedSweetness == "Less Sweet",
              () {
                setState(() {
                  selectedSweetness = "Less Sweet";
                });
              },
            ),
            const SizedBox(width: 12),
            _buildOptionButton("No Sweet", selectedSweetness == "No Sweet", () {
              setState(() {
                selectedSweetness = "No Sweet";
              });
            }),
          ],
        ),
        const SizedBox(height: 24),

        // Topping
        const Text(
          "Topping",
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildOptionButton("Normal", selectedTopping == "Normal", () {
              setState(() {
                selectedTopping = "Normal";
              });
            }),
            const SizedBox(width: 12),
            _buildOptionButton("Caramel", selectedTopping == "Caramel", () {
              setState(() {
                selectedTopping = "Caramel";
              });
            }),
            const SizedBox(width: 12),
            _buildOptionButton("Cookies", selectedTopping == "Cookies", () {
              setState(() {
                selectedTopping = "Cookies";
              });
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildOptionButton(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green[100] : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.green : Colors.grey,
            width: 1,
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isSelected ? Colors.green[700] : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
