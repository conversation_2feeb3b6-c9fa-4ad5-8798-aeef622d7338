import 'package:flutter/material.dart';
import 'package:test_pro/models/notification_model.dart';
import 'package:test_pro/services/notification_service.dart';
import 'package:test_pro/services/auth_service.dart';
import 'package:test_pro/Widgets/notifications/notification_item.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  List<NotificationItem> _notifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAuthAndLoadNotifications();
  }

  Future<void> _checkAuthAndLoadNotifications() async {
    // Check if user is logged in
    final isLoggedIn = await AuthService().isLoggedIn();

    if (!isLoggedIn && mounted) {
      // Show login alert and go back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AuthService.showLoginAlert(
          context,
          message: 'Please log in to view your notifications.',
        );
        Navigator.of(context).pop();
      });
      return;
    }

    // Load notifications for logged-in users
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final notifications = await NotificationService.getNotifications();
      setState(() {
        _notifications = notifications;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text(
            'Notification',
            style: TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final groupedNotifications = NotificationService.groupNotificationsByDate(_notifications);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Notification',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          if (_notifications.any((n) => !n.isRead))
            TextButton(
              onPressed: () async {
                await NotificationService.markAllAsRead();
                await _loadNotifications();
              },
              child: const Text(
                'Mark all read',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
      body: _notifications.isEmpty
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.notifications_none,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No notifications yet',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            )
          : ListView(
              padding: const EdgeInsets.all(16),
              children: _buildNotificationSections(groupedNotifications),
            ),
    );
  }

  List<Widget> _buildNotificationSections(Map<String, List<NotificationItem>> groupedNotifications) {
    final List<Widget> widgets = [];

    for (final entry in groupedNotifications.entries) {
      final sectionTitle = entry.key;
      final notifications = entry.value;

      widgets.add(_buildSectionHeader(sectionTitle));
      widgets.add(const SizedBox(height: 12));

      for (int i = 0; i < notifications.length; i++) {
        final notification = notifications[i];
        widgets.add(NotificationItemWidget(
          notification: notification,
          onTap: () => _loadNotifications(),
        ));
        if (i < notifications.length - 1) {
          widgets.add(const SizedBox(height: 16));
        }
      }

      widgets.add(const SizedBox(height: 24));
    }

    return widgets;
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  
}