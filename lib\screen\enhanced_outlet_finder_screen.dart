import 'package:flutter/material.dart';
import 'package:test_pro/models/outlet_model.dart';

class EnhancedOutletFinderScreen extends StatefulWidget {
  const EnhancedOutletFinderScreen({super.key});

  @override
  State<EnhancedOutletFinderScreen> createState() => _EnhancedOutletFinderScreenState();
}

class _EnhancedOutletFinderScreenState extends State<EnhancedOutletFinderScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _showOutletDetails = false;
  bool _showFilters = false;
  final List<Outlet> _outlets = sampleOutlets;
  List<Outlet> _filteredOutlets = sampleOutlets;
  Outlet? _selectedOutlet;

  // Filter options
  bool _filterPickup = false;
  bool _filterDelivery = false;
  bool _filterDineIn = false;
  bool _filterOperationalOnly = false;
  double _minRating = 0.0;

  // Favorites (in a real app, this would be stored in SharedPreferences or a database)
  final Set<String> _favoriteOutlets = {};

  @override
  void initState() {
    super.initState();
    _selectedOutlet = _outlets.first;
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    _applyFilters();
  }

  void _applyFilters() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredOutlets = _outlets.where((outlet) {
        // Text search
        bool matchesSearch = outlet.name.toLowerCase().contains(query) ||
                           outlet.address.toLowerCase().contains(query);

        // Service filters
        bool matchesServices = true;
        if (_filterPickup || _filterDelivery || _filterDineIn) {
          matchesServices = false;
          if (_filterPickup && outlet.hasPickup) matchesServices = true;
          if (_filterDelivery && outlet.hasDelivery) matchesServices = true;
          if (_filterDineIn && outlet.hasDineIn) matchesServices = true;
        }

        // Operational filter
        bool matchesOperational = !_filterOperationalOnly || outlet.isOperational;

        // Rating filter
        bool matchesRating = outlet.rating == null || outlet.rating! >= _minRating;

        return matchesSearch && matchesServices && matchesOperational && matchesRating;
      }).toList();

      // If current selection is filtered out, select first available
      if (_selectedOutlet != null && !_filteredOutlets.contains(_selectedOutlet)) {
        _selectedOutlet = _filteredOutlets.isNotEmpty ? _filteredOutlets.first : null;
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _selectOutlet(Outlet outlet) {
    setState(() {
      _selectedOutlet = outlet;
      _showOutletDetails = false;
    });
  }

  void _toggleFavorite(String outletId) {
    setState(() {
      if (_favoriteOutlets.contains(outletId)) {
        _favoriteOutlets.remove(outletId);
      } else {
        _favoriteOutlets.add(outletId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Map Background
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/map_placeholder.png'),
                fit: BoxFit.cover,
              ),
            ),
            child: Container(
              color: Colors.grey.withValues(alpha: 0.1),
              child: CustomPaint(
                painter: MapPainter(),
              ),
            ),
          ),

          // Top Bar with Search and Filter
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // Back Button
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.black),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
                const SizedBox(width: 12),

                // Search Bar
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search outlets...',
                        hintStyle: TextStyle(color: Colors.grey[600]),
                        prefixIcon: const Icon(Icons.search, color: Colors.grey),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Filter Button
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: _showFilters ? Colors.brown : Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.filter_list,
                      color: _showFilters ? Colors.white : Colors.black,
                    ),
                    onPressed: () {
                      setState(() {
                        _showFilters = !_showFilters;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // Filter Panel
          if (_showFilters)
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 16,
              right: 16,
              child: _buildFilterPanel(),
            ),

          // Map Markers
          ..._filteredOutlets.asMap().entries.map((entry) {
            final index = entry.key;
            final outlet = entry.value;
            final isSelected = outlet.id == _selectedOutlet?.id;
            final isFavorite = _favoriteOutlets.contains(outlet.id);

            // Position outlets at different locations on the map
            double top = 200 + (index * 60.0);
            double left = 50 + (index * 80.0);

            // Keep within screen bounds
            if (left > MediaQuery.of(context).size.width - 100) {
              left = MediaQuery.of(context).size.width - 100;
              top += 60;
            }

            return Positioned(
              top: top,
              left: left,
              child: _buildEnhancedMapMarker(
                outlet: outlet,
                isSelected: isSelected,
                isFavorite: isFavorite,
              ),
            );
          }),

          // Results Count
          if (_filteredOutlets.length != _outlets.length)
            Positioned(
              top: MediaQuery.of(context).padding.top + (_showFilters ? 200 : 80),
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.brown,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  '${_filteredOutlets.length} outlets found',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

          // Bottom Sheet - Outlet Info
          if (!_showOutletDetails && _selectedOutlet != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildEnhancedOutletPreview(),
            ),

          // Full Outlet Details
          if (_showOutletDetails && _selectedOutlet != null)
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 0,
              right: 0,
              bottom: 0,
              child: _buildEnhancedOutletDetails(),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Filters',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Service Filters
          const Text(
            'Services',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('Pickup'),
                selected: _filterPickup,
                onSelected: (selected) {
                  setState(() {
                    _filterPickup = selected;
                    _applyFilters();
                  });
                },
              ),
              FilterChip(
                label: const Text('Delivery'),
                selected: _filterDelivery,
                onSelected: (selected) {
                  setState(() {
                    _filterDelivery = selected;
                    _applyFilters();
                  });
                },
              ),
              FilterChip(
                label: const Text('Dine-in'),
                selected: _filterDineIn,
                onSelected: (selected) {
                  setState(() {
                    _filterDineIn = selected;
                    _applyFilters();
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Other Filters
          CheckboxListTile(
            title: const Text('Operational only'),
            value: _filterOperationalOnly,
            onChanged: (value) {
              setState(() {
                _filterOperationalOnly = value ?? false;
                _applyFilters();
              });
            },
            dense: true,
            contentPadding: EdgeInsets.zero,
          ),

          // Rating Filter
          const Text(
            'Minimum Rating',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          Slider(
            value: _minRating,
            min: 0.0,
            max: 5.0,
            divisions: 10,
            label: _minRating == 0.0 ? 'Any' : _minRating.toStringAsFixed(1),
            onChanged: (value) {
              setState(() {
                _minRating = value;
                _applyFilters();
              });
            },
          ),

          // Clear Filters
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _filterPickup = false;
                    _filterDelivery = false;
                    _filterDineIn = false;
                    _filterOperationalOnly = false;
                    _minRating = 0.0;
                    _applyFilters();
                  });
                },
                child: const Text('Clear All'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedMapMarker({
    required Outlet outlet,
    bool isSelected = false,
    bool isFavorite = false,
  }) {
    return GestureDetector(
      onTap: () {
        _selectOutlet(outlet);
      },
      child: Stack(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.orange
                  : (outlet.isOperational ? Colors.green : Colors.grey),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.local_cafe,
              color: Colors.white,
              size: 24,
            ),
          ),

          // Favorite indicator
          if (isFavorite)
            Positioned(
              top: -2,
              right: -2,
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.favorite,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),

          // Rating indicator
          if (outlet.rating != null)
            Positioned(
              bottom: -8,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  outlet.rating!.toStringAsFixed(1),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEnhancedOutletPreview() {
    if (_selectedOutlet == null) return const SizedBox.shrink();

    final isFavorite = _favoriteOutlets.contains(_selectedOutlet!.id);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Outlet Image
          Container(
            height: 120,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              image: DecorationImage(
                image: AssetImage(_selectedOutlet!.image ?? 'assets/images/outlet/default.jpg'),
                fit: BoxFit.cover,
                onError: (exception, stackTrace) {},
              ),
            ),
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.3),
                      ],
                    ),
                  ),
                ),

                // Favorite button
                Positioned(
                  top: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: () => _toggleFavorite(_selectedOutlet!.id),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite ? Colors.red : Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Outlet Info
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: _selectedOutlet!.isOperational ? Colors.green : Colors.grey,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _selectedOutlet!.name,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              if (_selectedOutlet!.rating != null) ...[
                                const SizedBox(width: 8),
                                Icon(Icons.star, color: Colors.amber, size: 16),
                                const SizedBox(width: 2),
                                Text(
                                  _selectedOutlet!.rating!.toStringAsFixed(1),
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _selectedOutlet!.address,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Services
                          Row(
                            children: [
                              if (_selectedOutlet!.hasPickup)
                                _buildServiceChip('Pickup', Icons.shopping_bag_outlined, Colors.green),
                              if (_selectedOutlet!.hasDelivery)
                                _buildServiceChip('Delivery', Icons.delivery_dining, Colors.blue),
                              if (_selectedOutlet!.hasDineIn)
                                _buildServiceChip('Dine-in', Icons.restaurant, Colors.orange),
                            ],
                          ),
                        ],
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _showOutletDetails = true;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.brown,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      child: const Text(
                        'INFO',
                        style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceChip(String label, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedOutletDetails() {
    if (_selectedOutlet == null) return const SizedBox.shrink();

    final isFavorite = _favoriteOutlets.contains(_selectedOutlet!.id);

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Close button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Outlet Details',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _showOutletDetails = false;
                          });
                        },
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Outlet Image with favorite button
                  Stack(
                    children: [
                      Container(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          image: DecorationImage(
                            image: AssetImage(_selectedOutlet!.image ?? 'assets/images/outlet/default.jpg'),
                            fit: BoxFit.cover,
                            onError: (exception, stackTrace) {},
                          ),
                        ),
                      ),
                      Positioned(
                        top: 8,
                        right: 8,
                        child: GestureDetector(
                          onTap: () => _toggleFavorite(_selectedOutlet!.id),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.5),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              isFavorite ? Icons.favorite : Icons.favorite_border,
                              color: isFavorite ? Colors.red : Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Outlet Name, Status, and Rating
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: _selectedOutlet!.isOperational ? Colors.green : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _selectedOutlet!.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (_selectedOutlet!.rating != null) ...[
                        Icon(Icons.star, color: Colors.amber, size: 20),
                        const SizedBox(width: 4),
                        Text(
                          _selectedOutlet!.rating!.toStringAsFixed(1),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (_selectedOutlet!.reviewCount != null) ...[
                          const SizedBox(width: 4),
                          Text(
                            '(${_selectedOutlet!.reviewCount})',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Address
                  Text(
                    _selectedOutlet!.address,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),

                  if (_selectedOutlet!.phone != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          _selectedOutlet!.phone!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],

                  const SizedBox(height: 24),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // TODO: Open maps with outlet location
                          },
                          icon: const Icon(Icons.directions),
                          label: const Text('Directions'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      if (_selectedOutlet!.phone != null)
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              // TODO: Make phone call
                            },
                            icon: const Icon(Icons.phone),
                            label: const Text('Call'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Status
                  const Text(
                    'Operating Hours',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Services
                  Row(
                    children: [
                      if (_selectedOutlet!.hasPickup)
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.green.withValues(alpha: 0.1),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.shopping_bag_outlined, size: 20, color: Colors.green),
                                SizedBox(width: 8),
                                Text('Pick Up', style: TextStyle(color: Colors.green, fontWeight: FontWeight.w600)),
                              ],
                            ),
                          ),
                        ),
                      if (_selectedOutlet!.hasPickup && _selectedOutlet!.hasDelivery)
                        const SizedBox(width: 12),
                      if (_selectedOutlet!.hasDelivery)
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.blue.withValues(alpha: 0.1),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.delivery_dining, size: 20, color: Colors.blue),
                                SizedBox(width: 8),
                                Text('Delivery', style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w600)),
                              ],
                            ),
                          ),
                        ),
                      if ((_selectedOutlet!.hasPickup || _selectedOutlet!.hasDelivery) && _selectedOutlet!.hasDineIn)
                        const SizedBox(width: 12),
                      if (_selectedOutlet!.hasDineIn)
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.orange.withValues(alpha: 0.1),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.restaurant, size: 20, color: Colors.orange),
                                SizedBox(width: 8),
                                Text('Dine-in', style: TextStyle(color: Colors.orange, fontWeight: FontWeight.w600)),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Operating Hours
                  ..._selectedOutlet!.operatingHours.entries.map<Widget>((entry) {
                    final isToday = _isToday(entry.key);
                    return Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      margin: const EdgeInsets.only(bottom: 4),
                      decoration: BoxDecoration(
                        color: isToday ? Colors.brown.withValues(alpha: 0.1) : null,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            entry.key,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                              color: isToday ? Colors.brown : Colors.black,
                            ),
                          ),
                          Text(
                            entry.value,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                              color: isToday ? Colors.brown : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(String dayName) {
    final now = DateTime.now();
    final today = _getDayName(now.weekday);
    return dayName == today;
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'Monday';
      case 2: return 'Tuesday';
      case 3: return 'Wednesday';
      case 4: return 'Thursday';
      case 5: return 'Friday';
      case 6: return 'Saturday';
      case 7: return 'Sunday';
      default: return 'Monday';
    }
  }
}

// Custom painter for map-like background (same as original)
class MapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withValues(alpha: 0.3)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    // Draw some river-like curves
    final path = Path();
    path.moveTo(size.width * 0.2, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.5, size.height * 0.3,
      size.width * 0.3, size.height * 0.6,
    );
    path.quadraticBezierTo(
      size.width * 0.1, size.height * 0.8,
      size.width * 0.4, size.height * 0.9,
    );

    canvas.drawPath(path, paint);

    // Draw some green areas (parks)
    final greenPaint = Paint()
      ..color = Colors.green.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(size.width * 0.7, size.height * 0.2),
      30,
      greenPaint,
    );

    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.7),
      25,
      greenPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}