class Outlet {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String? image;
  final bool isOperational;
  final Map<String, String> operatingHours;
  final List<String> services; // pickup, delivery, dine-in
  final String? phone;
  final double? rating;
  final int? reviewCount;

  Outlet({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.image,
    this.isOperational = true,
    required this.operatingHours,
    this.services = const ['pickup', 'delivery'],
    this.phone,
    this.rating,
    this.reviewCount,
  });

  factory Outlet.fromJson(Map<String, dynamic> json) {
    return Outlet(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      image: json['image'],
      isOperational: json['isOperational'] ?? true,
      operatingHours: Map<String, String>.from(json['operatingHours'] ?? {}),
      services: List<String>.from(json['services'] ?? ['pickup', 'delivery']),
      phone: json['phone'],
      rating: json['rating']?.toDouble(),
      reviewCount: json['reviewCount'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'image': image,
      'isOperational': isOperational,
      'operatingHours': operatingHours,
      'services': services,
      'phone': phone,
      'rating': rating,
      'reviewCount': reviewCount,
    };
  }

  bool get hasPickup => services.contains('pickup');
  bool get hasDelivery => services.contains('delivery');
  bool get hasDineIn => services.contains('dine-in');

  String get statusText => isOperational ? 'Operational' : 'Closed';

  // Get current operating status
  bool get isCurrentlyOpen {
    final now = DateTime.now();
    final dayName = _getDayName(now.weekday);
    final currentHours = operatingHours[dayName];
    
    if (currentHours == null || !isOperational) return false;
    
    // Parse hours like "08:00 AM - 10:00 PM"
    final parts = currentHours.split(' - ');
    if (parts.length != 2) return false;
    
    try {
      final openTime = _parseTime(parts[0]);
      final closeTime = _parseTime(parts[1]);
      final currentTime = now.hour * 60 + now.minute;
      
      return currentTime >= openTime && currentTime <= closeTime;
    } catch (e) {
      return false;
    }
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'Monday';
      case 2: return 'Tuesday';
      case 3: return 'Wednesday';
      case 4: return 'Thursday';
      case 5: return 'Friday';
      case 6: return 'Saturday';
      case 7: return 'Sunday';
      default: return 'Monday';
    }
  }

  int _parseTime(String timeStr) {
    // Parse time like "08:00 AM" or "10:00 PM"
    final parts = timeStr.trim().split(' ');
    if (parts.length != 2) throw FormatException('Invalid time format');
    
    final timeParts = parts[0].split(':');
    if (timeParts.length != 2) throw FormatException('Invalid time format');
    
    int hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);
    final period = parts[1].toUpperCase();
    
    if (period == 'PM' && hour != 12) {
      hour += 12;
    } else if (period == 'AM' && hour == 12) {
      hour = 0;
    }
    
    return hour * 60 + minute;
  }

  // Calculate distance from a given point (simplified)
  double distanceFrom(double lat, double lng) {
    // This is a simplified distance calculation
    // In a real app, you'd use a proper distance formula like Haversine
    final latDiff = latitude - lat;
    final lngDiff = longitude - lng;
    return (latDiff * latDiff + lngDiff * lngDiff) * 111; // Rough km conversion
  }
}

// Sample outlet data
final List<Outlet> sampleOutlets = [
  Outlet(
    id: '1',
    name: 'Outlet 1',
    address: '725 Melrose Avenue, Los Angeles, CA 90046',
    latitude: 34.0837,
    longitude: -118.3365,
    image: 'assets/images/outlet/outlet1.jpg',
    isOperational: true,
    operatingHours: {
      'Monday': '08:00 AM - 10:00 PM',
      'Tuesday': '08:00 AM - 10:00 PM',
      'Wednesday': '08:00 AM - 10:00 PM',
      'Thursday': '08:00 AM - 10:00 PM',
      'Friday': '08:00 AM - 10:00 PM',
      'Saturday': '08:00 AM - 10:00 PM',
      'Sunday': '08:00 AM - 10:00 PM',
    },
    services: ['pickup', 'delivery', 'dine-in'],
    phone: '+****************',
    rating: 4.5,
    reviewCount: 127,
  ),
  Outlet(
    id: '2',
    name: 'Outlet 2',
    address: '1234 Sunset Boulevard, Los Angeles, CA 90028',
    latitude: 34.0983,
    longitude: -118.3267,
    image: 'assets/images/outlet/outlet2.jpg',
    isOperational: true,
    operatingHours: {
      'Monday': '07:00 AM - 11:00 PM',
      'Tuesday': '07:00 AM - 11:00 PM',
      'Wednesday': '07:00 AM - 11:00 PM',
      'Thursday': '07:00 AM - 11:00 PM',
      'Friday': '07:00 AM - 12:00 AM',
      'Saturday': '07:00 AM - 12:00 AM',
      'Sunday': '08:00 AM - 10:00 PM',
    },
    services: ['pickup', 'delivery'],
    phone: '+****************',
    rating: 4.2,
    reviewCount: 89,
  ),
  Outlet(
    id: '3',
    name: 'Outlet 3',
    address: '5678 Hollywood Boulevard, Los Angeles, CA 90028',
    latitude: 34.1022,
    longitude: -118.3267,
    image: 'assets/images/outlet/outlet3.jpg',
    isOperational: false, // Temporarily closed
    operatingHours: {
      'Monday': '08:00 AM - 09:00 PM',
      'Tuesday': '08:00 AM - 09:00 PM',
      'Wednesday': '08:00 AM - 09:00 PM',
      'Thursday': '08:00 AM - 09:00 PM',
      'Friday': '08:00 AM - 10:00 PM',
      'Saturday': '08:00 AM - 10:00 PM',
      'Sunday': '09:00 AM - 08:00 PM',
    },
    services: ['pickup'],
    phone: '+****************',
    rating: 4.0,
    reviewCount: 45,
  ),
];
