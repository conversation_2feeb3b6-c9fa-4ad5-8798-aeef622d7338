import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:test_pro/services/theme_service.dart';
import 'package:test_pro/controllers/language_controller.dart';

class SettingView extends StatefulWidget {
  const SettingView({super.key});

  @override
  State<SettingView> createState() => _SettingViewState();
}

class _SettingViewState extends State<SettingView> {
  final LanguageController languageController = Get.find<LanguageController>();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Settings Header
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Text(
            'setting'.tr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),

        // Language Setting
        Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: GetBuilder<LanguageController>(
            builder: (controller) => ListTile(
              onTap: () {
                controller.showLanguageDialog(context);
              },
              title: Text('language'.tr),
              subtitle: Text(controller.currentLanguageName),
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    controller.currentLanguageFlag,
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(width: 8),
                  const Icon(Icons.language),
                ],
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    controller.currentLanguageCode == 'kh_KM' ? 'ខ្មែរ' : 'EN',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Icon(Icons.arrow_forward_ios, size: 16),
                ],
              ),
            ),
          ),
        ),

        // Theme Setting
        Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            onTap: () {
              _showThemeDialog(context);
            },
            title: Text('theme'.tr),
            leading: AnimatedBuilder(
              animation: ThemeService(),
              builder: (context, child) {
                return Icon(ThemeService().themeIcon);
              },
            ),
            subtitle: AnimatedBuilder(
              animation: ThemeService(),
              builder: (context, child) {
                return Text(ThemeService().themeModeString);
              },
            ),
            trailing: const Icon(Icons.arrow_forward_ios),
          ),
        ),
      ],
    );
  }

  void _showThemeDialog(BuildContext context) {
    Get.bottomSheet(
      Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                'chooseTheme'.tr,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Light Theme Option
            ListTile(
              leading: const Icon(Icons.light_mode),
              title: Text('lightTheme'.tr),
              trailing: ThemeService().isLightMode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                await ThemeService().setLightTheme();
                Get.back();
              },
            ),

            // Dark Theme Option
            ListTile(
              leading: const Icon(Icons.dark_mode),
              title: Text('darkTheme'.tr),
              trailing: ThemeService().isDarkMode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                await ThemeService().setDarkTheme();
                Get.back();
              },
            ),

            // System Theme Option
            ListTile(
              leading: const Icon(Icons.brightness_auto),
              title: Text('systemTheme'.tr),
              trailing: ThemeService().isSystemMode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                await ThemeService().setSystemTheme();
                Get.back();
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
    );
  }
}
