import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:test_pro/models/home/<USER>';

class SearchService {
  static Future<String> _getRecentSearchesFilePath() async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}/recent_searches.json';
  }

  // Get all products (you can modify this to load from your actual data source)
  static List<Product> getAllProducts() {
    return [
      Product(
        id: 1,
        name: 'Espresso',
        category: 'Coffee',
        price: 3.50,
        quantity: 1,
        image: 'assets/images/coffee/espresso.png', // Use correct existing image
        description: 'Rich and bold espresso shot with intense flavor',
      ),
      Product(
        id: 2,
        name: 'Cappuccino',
        category: 'Coffee',
        price: 4.25,
        quantity: 1,
        image: 'assets/images/coffee/cappuccino.png', // Use correct existing image
        description: 'Creamy cappuccino with steamed milk and foam',
      ),
      Product(
        id: 3,
        name: 'Latte',
        category: 'Coffee',
        price: 4.50,
        quantity: 1,
        image: 'assets/images/coffee/latte.png', // Use the actual latte.png file
        description: 'Smooth latte with steamed milk and beautiful foam art',
      ),
      Product(
        id: 4,
        name: 'Americano',
        category: 'Coffee',
        price: 3.75,
        quantity: 1,
        image: 'assets/images/coffee/amercano.png', // Note: your file is named "amercano.png"
        description: 'Classic americano coffee with hot water and espresso',
      ),
      Product(
        id: 5,
        name: 'Mocha',
        category: 'Coffee',
        price: 5.00,
        quantity: 1,
        image: 'assets/images/coffee/cfe.png', // Use existing image as fallback
        description: 'Chocolate mocha delight with whipped cream',
      ),
      Product(
        id: 6,
        name: 'Macchiato',
        category: 'Coffee',
        price: 4.75,
        quantity: 1,
        image: 'assets/images/coffee/hot_latte.png', // Use existing image as fallback
        description: 'Caramel macchiato perfection with vanilla syrup',
      ),
      Product(
        id: 7,
        name: 'Frappuccino',
        category: 'Cold Coffee',
        price: 5.25,
        quantity: 1,
        image: 'assets/images/coffee/ice_latte.png', // Use existing image as fallback
        description: 'Iced blended coffee drink with whipped cream',
      ),
      Product(
        id: 8,
        name: 'Cold Brew',
        category: 'Cold Coffee',
        price: 4.00,
        quantity: 1,
        image: 'assets/images/coffee/ice_latte copy.png', // Use existing image
        description: 'Smooth cold brew coffee steeped for 12 hours',
      ),
      Product(
        id: 9,
        name: 'Iced Latte',
        category: 'Cold Coffee',
        price: 4.75,
        quantity: 1,
        image: 'assets/images/coffee/ice_latte.png', // Use existing image
        description: 'Refreshing iced latte with cold milk',
      ),
      Product(
        id: 10,
        name: 'Green Tea',
        category: 'Tea',
        price: 3.25,
        quantity: 1,
        image: 'assets/images/tea/green_tea.png',
        description: 'Fresh green tea with antioxidants',
      ),
    ];
  }

  // Search products by name, description, or category
  static List<Product> searchProducts(String query) {
    if (query.isEmpty) return [];

    final allProducts = getAllProducts();
    final lowercaseQuery = query.toLowerCase();

    return allProducts.where((product) {
      return product.name.toLowerCase().contains(lowercaseQuery) ||
             product.description?.toLowerCase().contains(lowercaseQuery) == true ||
             product.category.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Get products by category
  static List<Product> getProductsByCategory(String category) {
    final allProducts = getAllProducts();
    return allProducts.where((product) => 
        product.category.toLowerCase() == category.toLowerCase()).toList();
  }

  // Get recent searches
  static Future<List<String>> getRecentSearches() async {
    try {
      final path = await _getRecentSearchesFilePath();
      final file = File(path);

      if (!(await file.exists())) {
        return [];
      }

      final content = await file.readAsString();
      final List<dynamic> jsonList = jsonDecode(content);
      return jsonList.cast<String>();
    } catch (e) {
      return [];
    }
  }

  // Save recent searches
  static Future<void> _saveRecentSearches(List<String> searches) async {
    try {
      final path = await _getRecentSearchesFilePath();
      final file = File(path);
      await file.writeAsString(jsonEncode(searches));
    } catch (e) {
      // Handle error silently
    }
  }

  // Add to recent searches
  static Future<void> addToRecentSearches(String query) async {
    if (query.trim().isEmpty) return;

    final recentSearches = await getRecentSearches();
    
    // Remove if already exists
    recentSearches.remove(query);
    
    // Add to beginning
    recentSearches.insert(0, query);
    
    // Keep only last 10 searches
    if (recentSearches.length > 10) {
      recentSearches.removeRange(10, recentSearches.length);
    }

    await _saveRecentSearches(recentSearches);
  }

  // Clear recent searches
  static Future<void> clearRecentSearches() async {
    await _saveRecentSearches([]);
  }

  // Get popular searches (you can customize this based on your needs)
  static List<String> getPopularSearches() {
    return [
      'Espresso',
      'Latte',
      'Cappuccino',
      'Americano',
      'Mocha',
      'Cold Brew',
      'Frappuccino',
      'Green Tea',
    ];
  }

  // Get search suggestions based on partial query
  static List<String> getSearchSuggestions(String query) {
    if (query.isEmpty) return getPopularSearches();

    final allProducts = getAllProducts();
    final suggestions = <String>{};
    final lowercaseQuery = query.toLowerCase();

    // Add product names that start with the query
    for (final product in allProducts) {
      if (product.name.toLowerCase().startsWith(lowercaseQuery)) {
        suggestions.add(product.name);
      }
    }

    // Add product names that contain the query
    for (final product in allProducts) {
      if (product.name.toLowerCase().contains(lowercaseQuery) &&
          !product.name.toLowerCase().startsWith(lowercaseQuery)) {
        suggestions.add(product.name);
      }
    }

    // Add categories that match
    final categories = allProducts.map((p) => p.category).toSet();
    for (final category in categories) {
      if (category.toLowerCase().contains(lowercaseQuery)) {
        suggestions.add(category);
      }
    }

    return suggestions.take(8).toList();
  }

  // Filter products by price range
  static List<Product> filterProductsByPriceRange(
      List<Product> products, double minPrice, double maxPrice) {
    return products.where((product) => 
        product.price >= minPrice && product.price <= maxPrice).toList();
  }

  // Sort products
  static List<Product> sortProducts(List<Product> products, String sortBy) {
    final sortedProducts = List<Product>.from(products);
    
    switch (sortBy.toLowerCase()) {
      case 'name':
        sortedProducts.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'price_low_to_high':
        sortedProducts.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_high_to_low':
        sortedProducts.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'category':
        sortedProducts.sort((a, b) => a.category.compareTo(b.category));
        break;
      default:
        // Keep original order
        break;
    }
    
    return sortedProducts;
  }
}
