import 'package:flutter/material.dart';
import 'package:test_pro/Widgets/card_checkout/item_section.dart';
import 'package:test_pro/Widgets/card_checkout/other_summary.dart';
import 'package:test_pro/Widgets/card_checkout/payment_button.dart';
import 'package:test_pro/Widgets/card_checkout/payment_section.dart';
import 'package:test_pro/models/cart_model.dart';
import 'package:test_pro/services/cart_service.dart';
import 'package:test_pro/services/cart_notifier_service.dart';
import 'package:test_pro/services/auth_service.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> with CartMixin {
  List<CartItem> _cart = [];
  bool _isLoading = true;
  bool _isGuest = false;

  @override
  void initState() {
    super.initState();
    _checkAuthAndLoadCart();
  }

  Future<void> _checkAuthAndLoadCart() async {
    // Check if user is logged in
    final isLoggedIn = await AuthService().isLoggedIn();

    if (!isLoggedIn) {
      // Show empty cart with login prompt for guests
      setState(() {
        _cart = [];
        _isLoading = false;
        _isGuest = true;
      });
      return;
    }

    // Load cart for logged-in users
    setState(() {
      _isGuest = false;
    });
    _loadCart();
  }

  Future<void> _loadCart() async {
    setState(() {
      _isLoading = true;
    });

    final cart = await CartService.getCartItems();
    setState(() {
      _cart = cart;
      _isLoading = false;
    });
  }

  Future<void> _updateQuantity(int productId, int newQuantity) async {
    await updateCartQuantity(productId, newQuantity);
    await _loadCart();
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Center(
          child: Text(
            "Checkout",
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ),
        actions: [
          SizedBox(width: 50,),
        ],
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _cart.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.shopping_cart_outlined,
                        size: 80,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _isGuest ? 'Please log in to view your cart' : 'Your cart is empty',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                      if (_isGuest) ...[
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            AuthService.showLoginAlert(
                              context,
                              message: 'Please log in to access your cart.',
                            );
                          },
                          child: const Text('Log In'),
                        ),
                      ],
                    ],
                  ),
                )
              : Stack(
                children: [
                  Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              //PickupSection(),
                              //const SizedBox(height: 24),
                              ItemSection(
                                cartItems: _cart,
                                onUpdateQuantity: _updateQuantity,
                              ),
                              //VocherSection(),
                              OtherSummary(cartItems: _cart),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 100),
                      PaymentSection(),
                    ],
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: PaymentButton(),
                  ),
                ],

              ),
    );
  }
  
}
