import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:test_pro/Widgets/product_detail/btn_add_to_card.dart';
import 'package:test_pro/models/home/<USER>';

void main() {
  group('AddToCartButton Widget Tests', () {
    testWidgets('should display add to cart button with correct text and price', (WidgetTester tester) async {
      // Create a test product
      final product = Product(
        id: 1,
        name: 'Test Coffee',
        category: 'Coffee',
        price: 4.50,
        quantity: 1,
        image: 'assets/images/coffee/espresso.png',
        description: 'Test coffee description',
      );

      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddToCartButton(
              product: product,
              quantity: 2,
            ),
          ),
        ),
      );

      // Verify the widget is rendered
      expect(find.byType(AddToCartButton), findsOneWidget);
      expect(find.text('Add to Cart'), findsOneWidget);
      expect(find.text('\$9.00'), findsOneWidget); // 4.50 * 2 = 9.00
      expect(find.byIcon(Icons.shopping_cart), findsOneWidget);
    });

    testWidgets('should display correct price for different quantities', (WidgetTester tester) async {
      final product = Product(
        id: 2,
        name: 'Latte',
        category: 'Coffee',
        price: 5.25,
        quantity: 1,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddToCartButton(
              product: product,
              quantity: 3,
            ),
          ),
        ),
      );

      // Verify the price calculation (5.25 * 3 = 15.75)
      expect(find.text('\$15.75'), findsOneWidget);
    });

    testWidgets('should have correct widget structure', (WidgetTester tester) async {
      final product = Product(
        id: 3,
        name: 'Cappuccino',
        category: 'Coffee',
        price: 4.00,
        quantity: 1,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddToCartButton(product: product),
          ),
        ),
      );

      // Verify the essential widget structure
      expect(find.byType(AddToCartButton), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('Add to Cart'), findsOneWidget);
    });

    testWidgets('should handle default quantity when not specified', (WidgetTester tester) async {
      final product = Product(
        id: 4,
        name: 'Mocha',
        category: 'Coffee',
        price: 6.00,
        quantity: 1,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddToCartButton(product: product),
          ),
        ),
      );

      // Should default to quantity 1, so price should be 6.00
      expect(find.text('\$6.00'), findsOneWidget);
    });

    testWidgets('should be tappable', (WidgetTester tester) async {
      final product = Product(
        id: 5,
        name: 'Americano',
        category: 'Coffee',
        price: 3.75,
        quantity: 1,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddToCartButton(product: product),
          ),
        ),
      );

      // Verify the button exists and can be tapped
      final buttonFinder = find.byType(ElevatedButton);
      expect(buttonFinder, findsOneWidget);

      // Verify button is enabled
      final ElevatedButton button = tester.widget(buttonFinder);
      expect(button.onPressed, isNotNull);
    });

    testWidgets('should handle custom selections', (WidgetTester tester) async {
      final product = Product(
        id: 6,
        name: 'Custom Coffee',
        category: 'Coffee',
        price: 5.50,
        quantity: 1,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddToCartButton(
              product: product,
              quantity: 1,
              selectedTemperature: 'Hot',
              selectedSize: 'Large',
              selectedSweetness: 'Extra Sweet',
              selectedTopping: 'Whipped Cream',
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(AddToCartButton), findsOneWidget);
      expect(find.text('\$5.50'), findsOneWidget);
    });
  });
}
