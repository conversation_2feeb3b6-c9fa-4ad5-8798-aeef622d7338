// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAxvUG6vUeUE22Zr8VXagnedFCqTk4wwAc',
    appId: '1:1066450000420:web:7527086b272e0bf23616b1',
    messagingSenderId: '1066450000420',
    projectId: 'coffee-vortex',
    authDomain: 'coffee-vortex.firebaseapp.com',
    storageBucket: 'coffee-vortex.firebasestorage.app',
    measurementId: 'G-G1K7E0PPTC',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBxjdGla0AfgWl7cydx-r6iRyMahwfQdX8',
    appId: '1:1066450000420:android:4a88ae4e3c293e7b3616b1',
    messagingSenderId: '1066450000420',
    projectId: 'coffee-vortex',
    storageBucket: 'coffee-vortex.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAFnci0tZ6lZtaFnjvGzCRf9XmtCsVzmCY',
    appId: '1:1066450000420:ios:1a897ae835776e413616b1',
    messagingSenderId: '1066450000420',
    projectId: 'coffee-vortex',
    storageBucket: 'coffee-vortex.firebasestorage.app',
    iosBundleId: 'com.example.coffeVortex',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAFnci0tZ6lZtaFnjvGzCRf9XmtCsVzmCY',
    appId: '1:1066450000420:ios:1a897ae835776e413616b1',
    messagingSenderId: '1066450000420',
    projectId: 'coffee-vortex',
    storageBucket: 'coffee-vortex.firebasestorage.app',
    iosBundleId: 'com.example.coffeVortex',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAxvUG6vUeUE22Zr8VXagnedFCqTk4wwAc',
    appId: '1:1066450000420:web:9467185cb917c3953616b1',
    messagingSenderId: '1066450000420',
    projectId: 'coffee-vortex',
    authDomain: 'coffee-vortex.firebaseapp.com',
    storageBucket: 'coffee-vortex.firebasestorage.app',
    measurementId: 'G-J3JS25CBJN',
  );

}