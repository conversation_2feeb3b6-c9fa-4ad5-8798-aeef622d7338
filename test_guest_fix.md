# Guest Login Fix Test

## Issue Description
When clicking "continue" as a guest, the app was still catching/using stored user credentials instead of properly setting the user as a guest.

## Fix Applied
Modified the `_textLoginWith` widget in `lib/screen/Login/login_screen.dart` to:

1. **Clear stored credentials** - Remove any stored username, email, and password from SharedPreferences
2. **Set guest state** - Use the UserController's `clearUser()` method to properly set the user as a guest
3. **Navigate to main screen** - Continue to the main screen as a proper guest user

## Code Changes
```dart
Widget get _textLoginWith {
  return GestureDetector(
    onTap: () async {
      // Clear any stored credentials and set user as guest
      await _continueAsGuest();
      AppRoute.key.currentState?.pushNamed(AppRoute.mainScreen);
    },
    child: Text('continue'.tr),
  );
}

/// Continue as guest - clear stored credentials and set guest state
Future<void> _continueAsGuest() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    
    // Clear any stored login credentials
    await prefs.remove('username');
    await prefs.remove('email');
    await prefs.remove('password');
    
    // Clear user data and set as guest in the controller
    final userController = Get.find<UserController>();
    userController.clearUser();
    
  } catch (e) {
    // Handle error silently - guest mode should always work
  }
}
```

## How to Test
1. **Launch the app**
2. **Go to login screen**
3. **Click "continue" (guest login)**
4. **Verify the user is properly set as "Guest"**
5. **Check that no stored credentials are being used**

## Expected Behavior After Fix
- User should be properly identified as "Guest"
- No stored email/username should be displayed
- Guest restrictions should apply correctly
- App should not try to use any previously stored credentials

## Technical Details
- The fix ensures SharedPreferences are cleared of any login data
- UserController.clearUser() properly sets the user profile to Guest
- AuthService.isLoggedIn() will return false for guest users
- AuthService.isGuest() will return true for guest users
