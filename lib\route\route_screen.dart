import 'package:flutter/material.dart';
import 'package:test_pro/screen/Login/forgot_password.dart';
import 'package:test_pro/screen/Login/login_screen.dart';
import 'package:test_pro/screen/Login/verifications.dart';
import 'package:test_pro/screen/Register/register_screen.dart';
import 'package:test_pro/screen/main_screen.dart';
import 'package:test_pro/screen/splash_screen.dart';
import 'package:test_pro/screen/welcome_screen.dart';
import 'package:test_pro/screen/edit_profile_screen.dart';

class AppRoute {
  static const String splashScreen = "";
  static const String welcome = "welcomeScreen";
  static const String welcomeScreen = "welcomeScreen";
  static const String loginScreen = "loginScreen";
  static const String registerScreen = "registerScreen";
  static const String mainScreen = "mainScreen";
  static const String verificationsScreen = "verifications";
  static const String forgotPassword = "forgotPassword";
  static const String editProfileScreen = "editProfileScreen";

  static final key = GlobalKey<NavigatorState>();

  static Route<dynamic> onGenerateRoute(RouteSettings routeSettings) {
    switch (routeSettings.name) {
      case splashScreen:
        return _buildRoute(routeSettings, SplashScreen());
      case welcomeScreen:
        return _buildRoute(routeSettings, WelcomeScreen());
      case loginScreen:
        return _buildRoute(routeSettings, LoginScreen());
      case registerScreen:
        return _buildRoute(routeSettings, RegisterScreen());
      case mainScreen:
        return _buildRoute(routeSettings, MainScreen());
      case verificationsScreen:
        return _buildRoute(routeSettings, Verifications());
      case forgotPassword:
        return _buildRoute(routeSettings, ForgotPassword());
      case editProfileScreen:
        return _buildRoute(routeSettings, EditProfileScreen());
      default:
        throw RouteException("Route not found");
    }
  }

  static Route<dynamic> _buildRoute(
      RouteSettings routeSettings, Widget newScreen) {
    return MaterialPageRoute(
        settings: routeSettings, builder: (BuildContext context) => newScreen);
  }
}

class RouteException implements Exception {
  String message;
  RouteException(this.message);
}
