import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test_pro/services/auth_service.dart';

class OutletFavoritesService {
  static const String _favoritesKey = 'outlet_favorites';
  
  // Singleton pattern
  static final OutletFavoritesService _instance = OutletFavoritesService._internal();
  factory OutletFavoritesService() => _instance;
  OutletFavoritesService._internal();

  // Cache for favorites
  Set<String>? _cachedFavorites;

  /// Get all favorite outlet IDs
  Future<Set<String>> getFavorites() async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return <String>{}; // Return empty set for guest users
    }

    if (_cachedFavorites != null) {
      return Set.from(_cachedFavorites!);
    }

    final prefs = await SharedPreferences.getInstance();
    final favoritesList = prefs.getStringList(_favoritesKey) ?? [];
    _cachedFavorites = Set.from(favoritesList);
    return Set.from(_cachedFavorites!);
  }

  /// Add an outlet to favorites
  Future<bool> addFavorite(String outletId) async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return false; // Don't save favorites for guest users
    }

    try {
      final favorites = await getFavorites();
      favorites.add(outletId);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_favoritesKey, favorites.toList());
      
      _cachedFavorites = favorites;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Remove an outlet from favorites
  Future<bool> removeFavorite(String outletId) async {
    // Check authentication first
    final isLoggedIn = await AuthService().isLoggedIn();
    if (!isLoggedIn) {
      return false; // Don't modify favorites for guest users
    }

    try {
      final favorites = await getFavorites();
      favorites.remove(outletId);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_favoritesKey, favorites.toList());
      
      _cachedFavorites = favorites;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Toggle favorite status of an outlet
  Future<bool> toggleFavorite(String outletId) async {
    final favorites = await getFavorites();
    if (favorites.contains(outletId)) {
      return await removeFavorite(outletId);
    } else {
      return await addFavorite(outletId);
    }
  }

  /// Check if an outlet is in favorites
  Future<bool> isFavorite(String outletId) async {
    final favorites = await getFavorites();
    return favorites.contains(outletId);
  }

  /// Clear all favorites
  Future<bool> clearFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoritesKey);
      _cachedFavorites = <String>{};
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get count of favorite outlets
  Future<int> getFavoritesCount() async {
    final favorites = await getFavorites();
    return favorites.length;
  }

  /// Clear cache (useful when app restarts or data changes externally)
  void clearCache() {
    _cachedFavorites = null;
  }
}

// Extension to make it easier to use with outlets
extension OutletFavoritesExtension on OutletFavoritesService {
  /// Get favorite outlets from a list of all outlets
  Future<List<T>> getFavoriteOutlets<T>(
    List<T> allOutlets,
    String Function(T) getOutletId,
  ) async {
    final favorites = await getFavorites();
    return allOutlets.where((outlet) => favorites.contains(getOutletId(outlet))).toList();
  }

  /// Filter outlets by favorite status
  Future<List<T>> filterOutletsByFavoriteStatus<T>(
    List<T> outlets,
    String Function(T) getOutletId,
    bool showOnlyFavorites,
  ) async {
    if (!showOnlyFavorites) return outlets;
    
    final favorites = await getFavorites();
    return outlets.where((outlet) => favorites.contains(getOutletId(outlet))).toList();
  }
}

// Mixin for widgets that need to manage favorites
mixin OutletFavoritesMixin<T extends StatefulWidget> on State<T> {
  final OutletFavoritesService _favoritesService = OutletFavoritesService();
  Set<String> _favoriteOutlets = {};

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    final favorites = await _favoritesService.getFavorites();
    if (mounted) {
      setState(() {
        _favoriteOutlets = favorites;
      });
    }
  }

  Future<void> toggleOutletFavorite(String outletId) async {
    final success = await _favoritesService.toggleFavorite(outletId);
    if (success && mounted) {
      final favorites = await _favoritesService.getFavorites();
      setState(() {
        _favoriteOutlets = favorites;
      });
    }
  }

  bool isOutletFavorite(String outletId) {
    return _favoriteOutlets.contains(outletId);
  }

  Set<String> get favoriteOutlets => Set.from(_favoriteOutlets);
}

// Stream-based favorites notifier for real-time updates across screens
class OutletFavoritesNotifier {
  static final OutletFavoritesNotifier _instance = OutletFavoritesNotifier._internal();
  factory OutletFavoritesNotifier() => _instance;
  OutletFavoritesNotifier._internal();

  final OutletFavoritesService _service = OutletFavoritesService();
  
  // Stream controller for favorites changes
  final List<Function(Set<String>)> _listeners = [];

  /// Add a listener for favorites changes
  void addListener(Function(Set<String>) listener) {
    _listeners.add(listener);
  }

  /// Remove a listener
  void removeListener(Function(Set<String>) listener) {
    _listeners.remove(listener);
  }

  /// Notify all listeners of favorites change
  void _notifyListeners(Set<String> favorites) {
    for (final listener in _listeners) {
      listener(favorites);
    }
  }

  /// Toggle favorite and notify listeners
  Future<bool> toggleFavorite(String outletId) async {
    final success = await _service.toggleFavorite(outletId);
    if (success) {
      final favorites = await _service.getFavorites();
      _notifyListeners(favorites);
    }
    return success;
  }

  /// Add favorite and notify listeners
  Future<bool> addFavorite(String outletId) async {
    final success = await _service.addFavorite(outletId);
    if (success) {
      final favorites = await _service.getFavorites();
      _notifyListeners(favorites);
    }
    return success;
  }

  /// Remove favorite and notify listeners
  Future<bool> removeFavorite(String outletId) async {
    final success = await _service.removeFavorite(outletId);
    if (success) {
      final favorites = await _service.getFavorites();
      _notifyListeners(favorites);
    }
    return success;
  }

  /// Get current favorites
  Future<Set<String>> getFavorites() => _service.getFavorites();

  /// Check if outlet is favorite
  Future<bool> isFavorite(String outletId) => _service.isFavorite(outletId);

  /// Clear all listeners (call in dispose)
  void dispose() {
    _listeners.clear();
  }
}
