import 'package:flutter/material.dart';
import 'package:test_pro/services/cart_service.dart';
import 'package:test_pro/utils/top_alert_utils.dart';
import 'package:test_pro/Widgets/card_checkout/payment/cart_payment_methods_screen.dart';
class PaymentButton extends StatefulWidget {
  const PaymentButton({super.key});

  @override
  State<PaymentButton> createState() => _PaymentButtonState();
}

class _PaymentButtonState extends State<PaymentButton> {
  Future<void> _navigateToPaymentMethods() async {
    // Get current cart items
    final cartItems = await CartService.getCartItems();

    if (cartItems.isEmpty) {
      if (mounted) {
        TopAlertUtils.showErrorAlert(
          context,
          'Your cart is empty. Add some items first!',
        );
      }
      return;
    }

    // Navigate to payment method selection
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CartPaymentMethodsScreen(
            cartItems: cartItems,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Si<PERSON>B<PERSON>(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: _navigateToPaymentMethods,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green[700],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text(
            'PAYMENT',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
