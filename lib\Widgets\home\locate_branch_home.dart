import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:test_pro/screen/outlet_finder_screen.dart';
import 'package:test_pro/theme/app_theme.dart';

class LocateBranchHome extends StatelessWidget {
  const LocateBranchHome({super.key});

  @override
  Widget build(BuildContext context) {
    final List<String> imgList = [
      "assets/images/coffee_shop.png",
      "assets/images/coffee_shop.png",
      "assets/images/coffee_shop.png",
    ];

    final List<String> branchName = ["Branch 1", "Branch 2", "Branch 3"];
    final List<String> branchAddress = ["Address 1", "Address 2", "Address 3"];
    final List<String> description = ["Description 1", "Description 2", "Description 3"];

    return Column(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const OutletFinderScreen(),
              ),
            );
          },
          child: Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppTheme.bgContainer,
              borderRadius: BorderRadius.circular(15),
            ),
            child: CarouselSlider.builder(
            itemCount: imgList.length,
            itemBuilder: (context, i, id) {
              return SizedBox(
                width: double.infinity,
                child: Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 10),
                            child: Text(
                              branchName[i],
                              style: const TextStyle(
                                fontSize: 25,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 10),
                            child: Text(
                              description[i],
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 10, top: 10),
                            child: Text(
                              branchAddress[i],
                              style: const TextStyle(fontSize: 20),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 8),

                    Expanded(
                      flex: 1,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          imgList[i],
                          fit: BoxFit.cover,
                          height: double.infinity,
                          width: double.infinity,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
            options: CarouselOptions(
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 30),
              height: 150,
              enlargeCenterPage: false,
              viewportFraction: 1.0,
            ),
          ),
        ),
        ),
      ],
    );
  }
}
